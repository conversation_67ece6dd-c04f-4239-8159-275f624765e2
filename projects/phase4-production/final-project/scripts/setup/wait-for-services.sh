#!/bin/bash

# 等待服务启动脚本
# 等待数据库和其他基础服务完全启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待MariaDB启动并初始化完成
wait_for_mariadb() {
    log_info "等待MariaDB启动并初始化..."
    
    # 等待MariaDB进程启动
    for i in {1..60}; do
        if docker exec ecommerce-mariadb mysqladmin ping -h localhost --silent > /dev/null 2>&1; then
            log_success "MariaDB进程已启动"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "MariaDB启动超时"
            return 1
        fi
        log_info "等待MariaDB启动... ($i/60)"
        sleep 2
    done
    
    # 等待数据库初始化完成
    log_info "等待数据库初始化完成..."
    for i in {1..30}; do
        if docker exec ecommerce-mariadb mysql -uecommerce -ppassword -e "SHOW DATABASES LIKE 'ecommerce_user'" | grep -q "ecommerce_user"; then
            log_success "数据库初始化完成"
            break
        fi
        if [ $i -eq 30 ]; then
            log_warning "数据库可能未完全初始化，但MariaDB已可用"
            break
        fi
        log_info "等待数据库初始化... ($i/30)"
        sleep 3
    done
}

# 等待Redis启动
wait_for_redis() {
    log_info "等待Redis启动..."
    
    for i in {1..30}; do
        if docker exec ecommerce-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
            log_success "Redis已启动"
            return 0
        fi
        if [ $i -eq 30 ]; then
            log_error "Redis启动超时"
            return 1
        fi
        log_info "等待Redis启动... ($i/30)"
        sleep 2
    done
}

# 等待RabbitMQ启动
wait_for_rabbitmq() {
    log_info "等待RabbitMQ启动..."
    
    for i in {1..60}; do
        if docker exec ecommerce-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
            log_success "RabbitMQ已启动"
            return 0
        fi
        if [ $i -eq 60 ]; then
            log_error "RabbitMQ启动超时"
            return 1
        fi
        log_info "等待RabbitMQ启动... ($i/60)"
        sleep 2
    done
}

# 测试数据库连接
test_database_connection() {
    log_info "测试数据库连接..."
    
    # 测试root连接
    if docker exec ecommerce-mariadb mysql -uroot -prootpassword -e "SELECT 1" > /dev/null 2>&1; then
        log_success "Root用户连接正常"
    else
        log_error "Root用户连接失败"
        return 1
    fi
    
    # 测试应用用户连接
    if docker exec ecommerce-mariadb mysql -uecommerce -ppassword -e "SELECT 1" > /dev/null 2>&1; then
        log_success "应用用户连接正常"
    else
        log_error "应用用户连接失败"
        return 1
    fi
    
    # 检查数据库是否存在
    databases=$(docker exec ecommerce-mariadb mysql -uecommerce -ppassword -e "SHOW DATABASES" 2>/dev/null | grep "ecommerce_" || true)
    if [ -n "$databases" ]; then
        log_success "应用数据库已创建:"
        echo "$databases" | sed 's/^/  /'
    else
        log_warning "应用数据库未找到，可能需要手动初始化"
    fi
}

# 测试Redis连接
test_redis_connection() {
    log_info "测试Redis连接..."
    
    # 测试基本连接
    if docker exec ecommerce-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
        log_success "Redis连接正常"
    else
        log_error "Redis连接失败"
        return 1
    fi
    
    # 测试读写操作
    if docker exec ecommerce-redis redis-cli set test_key "test_value" > /dev/null 2>&1 && \
       docker exec ecommerce-redis redis-cli get test_key 2>/dev/null | grep -q "test_value"; then
        log_success "Redis读写操作正常"
        docker exec ecommerce-redis redis-cli del test_key > /dev/null 2>&1
    else
        log_error "Redis读写操作失败"
        return 1
    fi
}

# 测试RabbitMQ连接
test_rabbitmq_connection() {
    log_info "测试RabbitMQ连接..."
    
    # 测试基本连接
    if docker exec ecommerce-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
        log_success "RabbitMQ连接正常"
    else
        log_error "RabbitMQ连接失败"
        return 1
    fi
    
    # 检查管理界面是否可访问
    if curl -s -f -u admin:password http://localhost:15672/api/overview > /dev/null 2>&1; then
        log_success "RabbitMQ管理界面可访问"
    else
        log_warning "RabbitMQ管理界面暂时不可访问"
    fi
}

# 显示服务状态
show_services_status() {
    echo ""
    echo "=========================================="
    echo "         服务状态总览"
    echo "=========================================="
    echo ""
    
    # 显示容器状态
    echo "容器状态："
    docker-compose -f docker-compose.dev.yml ps 2>/dev/null || echo "无法获取容器状态"
    
    echo ""
    echo "端口占用情况："
    echo "  3306 (MariaDB): $(netstat -an 2>/dev/null | grep :3306 | wc -l) 个连接"
    echo "  6379 (Redis): $(netstat -an 2>/dev/null | grep :6379 | wc -l) 个连接"
    echo "  5672 (RabbitMQ): $(netstat -an 2>/dev/null | grep :5672 | wc -l) 个连接"
    echo "  15672 (RabbitMQ管理): $(netstat -an 2>/dev/null | grep :15672 | wc -l) 个连接"
}

# 主函数
main() {
    echo "=========================================="
    echo "         等待服务启动完成"
    echo "=========================================="
    echo ""
    
    # 等待各个服务启动
    wait_for_mariadb
    echo ""
    
    wait_for_redis
    echo ""
    
    wait_for_rabbitmq
    echo ""
    
    # 测试服务连接
    echo "=========================================="
    echo "         测试服务连接"
    echo "=========================================="
    echo ""
    
    test_database_connection
    echo ""
    
    test_redis_connection
    echo ""
    
    test_rabbitmq_connection
    echo ""
    
    # 显示服务状态
    show_services_status
    
    echo ""
    echo "=========================================="
    log_success "所有服务已启动并可用！"
    echo "=========================================="
    echo ""
    echo "现在可以启动应用服务了。"
}

# 执行主函数
main "$@"
