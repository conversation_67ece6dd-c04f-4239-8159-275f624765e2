#!/bin/bash

# 启动基础设施服务脚本
# 启动MariaDB、Redis、RabbitMQ等基础服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker是否运行..."
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行，请先启动Docker"
        exit 1
    fi
    
    log_success "Docker运行正常"
}

# 检查docker-compose文件是否存在
check_compose_file() {
    log_info "检查docker-compose配置文件..."
    
    if [ ! -f "docker-compose.dev.yml" ]; then
        log_error "docker-compose.dev.yml文件不存在"
        exit 1
    fi
    
    log_success "docker-compose配置文件存在"
}

# 停止现有容器
stop_existing_containers() {
    log_info "停止现有容器..."
    
    # 停止可能存在的容器
    docker-compose -f docker-compose.dev.yml down > /dev/null 2>&1 || true
    
    log_success "现有容器已停止"
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动MariaDB、Redis、RabbitMQ
    docker-compose -f docker-compose.dev.yml up -d mariadb redis rabbitmq
    
    log_success "基础设施服务启动命令已执行"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待MariaDB启动
    log_info "等待MariaDB启动..."
    for i in {1..60}; do
        if docker exec ecommerce-mariadb mysqladmin ping -h localhost --silent > /dev/null 2>&1; then
            log_success "MariaDB已启动"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "MariaDB启动超时"
            return 1
        fi
        sleep 2
    done
    
    # 等待Redis启动
    log_info "等待Redis启动..."
    for i in {1..30}; do
        if docker exec ecommerce-redis redis-cli ping > /dev/null 2>&1; then
            log_success "Redis已启动"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Redis启动超时"
            return 1
        fi
        sleep 2
    done
    
    # 等待RabbitMQ启动
    log_info "等待RabbitMQ启动..."
    for i in {1..60}; do
        if docker exec ecommerce-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
            log_success "RabbitMQ已启动"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "RabbitMQ启动超时"
            return 1
        fi
        sleep 2
    done
}

# 检查服务状态
check_services_status() {
    log_info "检查服务状态..."
    
    echo ""
    echo "容器状态："
    docker-compose -f docker-compose.dev.yml ps
    
    echo ""
    echo "服务连接测试："
    
    # 测试MariaDB连接
    if docker exec ecommerce-mariadb mysql -uecommerce -ppassword -e "SELECT 1" > /dev/null 2>&1; then
        log_success "MariaDB连接正常"
    else
        log_error "MariaDB连接失败"
    fi
    
    # 测试Redis连接
    if docker exec ecommerce-redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis连接正常"
    else
        log_error "Redis连接失败"
    fi
    
    # 测试RabbitMQ连接
    if docker exec ecommerce-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
        log_success "RabbitMQ连接正常"
    else
        log_error "RabbitMQ连接失败"
    fi
}

# 显示服务信息
show_service_info() {
    echo ""
    echo "=========================================="
    echo "         服务连接信息"
    echo "=========================================="
    echo ""
    echo "MariaDB:"
    echo "  主机: localhost"
    echo "  端口: 3306"
    echo "  用户名: ecommerce"
    echo "  密码: password"
    echo "  数据库: ecommerce_user, ecommerce_product, etc."
    echo ""
    echo "Redis:"
    echo "  主机: localhost"
    echo "  端口: 6379"
    echo "  密码: (无)"
    echo ""
    echo "RabbitMQ:"
    echo "  主机: localhost"
    echo "  端口: 5672 (AMQP), 15672 (管理界面)"
    echo "  用户名: admin"
    echo "  密码: password"
    echo "  管理界面: http://localhost:15672"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "         启动基础设施服务"
    echo "=========================================="
    echo ""
    
    # 检查前置条件
    check_docker
    check_compose_file
    
    # 停止现有容器
    stop_existing_containers
    
    # 启动服务
    start_infrastructure
    
    # 等待服务启动
    wait_for_services
    
    # 检查服务状态
    check_services_status
    
    # 显示服务信息
    show_service_info
    
    log_success "基础设施服务启动完成！"
    echo ""
    echo "提示："
    echo "1. 可以使用 'docker-compose -f docker-compose.dev.yml logs -f' 查看日志"
    echo "2. 可以使用 'docker-compose -f docker-compose.dev.yml down' 停止服务"
    echo "3. RabbitMQ管理界面: http://localhost:15672 (admin/password)"
}

# 执行主函数
main "$@"
