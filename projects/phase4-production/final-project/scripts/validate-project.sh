#!/bin/bash

# 综合项目验证脚本
# 执行云原生电商平台的完整验证流程

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="${NAMESPACE:-ecommerce-prod}"
HELM_RELEASE="${HELM_RELEASE:-ecommerce-platform}"
VALIDATION_REPORT_DIR="validation-reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 验证选项
RUN_DEPLOYMENT_CHECK="${RUN_DEPLOYMENT_CHECK:-true}"
RUN_E2E_TESTS="${RUN_E2E_TESTS:-true}"
RUN_PERFORMANCE_TESTS="${RUN_PERFORMANCE_TESTS:-true}"
RUN_SECURITY_SCAN="${RUN_SECURITY_SCAN:-true}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_result() {
    echo -e "${CYAN}[RESULT]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                云原生电商平台项目验证                          ║
║                                                              ║
║  🚀 部署验证  🧪 端到端测试  ⚡ 性能测试  🔒 安全扫描        ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 创建验证报告目录
create_validation_report_dir() {
    mkdir -p "$VALIDATION_REPORT_DIR"
    log_info "创建验证报告目录: $VALIDATION_REPORT_DIR"
}

# 检查前置条件
check_prerequisites() {
    log_step "检查前置条件..."
    
    local tools=("kubectl" "helm" "curl" "jq")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少以下工具: ${missing_tools[*]}"
        exit 1
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 检查命名空间
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "命名空间 $NAMESPACE 不存在"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 运行部署验证
run_deployment_verification() {
    if [ "$RUN_DEPLOYMENT_CHECK" != "true" ]; then
        log_warning "跳过部署验证"
        return 0
    fi
    
    log_step "运行部署验证..."
    
    local deploy_script="./scripts/deployment/verify-deployment.sh"
    
    if [ -f "$deploy_script" ]; then
        chmod +x "$deploy_script"
        
        if "$deploy_script"; then
            log_success "部署验证通过"
            
            # 复制验证报告
            if [ -d "verification-reports" ]; then
                cp -r verification-reports/* "$VALIDATION_REPORT_DIR/" 2>/dev/null || true
            fi
            
            return 0
        else
            log_error "部署验证失败"
            return 1
        fi
    else
        log_warning "部署验证脚本不存在: $deploy_script"
        return 1
    fi
}

# 运行端到端测试
run_e2e_tests() {
    if [ "$RUN_E2E_TESTS" != "true" ]; then
        log_warning "跳过端到端测试"
        return 0
    fi
    
    log_step "运行端到端测试..."
    
    local e2e_script="./scripts/testing/e2e-test.sh"
    
    if [ -f "$e2e_script" ]; then
        chmod +x "$e2e_script"
        
        if "$e2e_script"; then
            log_success "端到端测试通过"
            
            # 复制测试报告
            if [ -d "e2e-test-reports" ]; then
                cp -r e2e-test-reports/* "$VALIDATION_REPORT_DIR/" 2>/dev/null || true
            fi
            
            return 0
        else
            log_error "端到端测试失败"
            return 1
        fi
    else
        log_warning "端到端测试脚本不存在: $e2e_script"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    if [ "$RUN_PERFORMANCE_TESTS" != "true" ]; then
        log_warning "跳过性能测试"
        return 0
    fi
    
    log_step "运行性能测试..."
    
    local perf_script="./scripts/testing/performance-test.sh"
    
    if [ -f "$perf_script" ]; then
        chmod +x "$perf_script"
        
        # 设置较轻的性能测试参数
        export CONCURRENT_USERS=5
        export REQUESTS_PER_SECOND=50
        
        if "$perf_script"; then
            log_success "性能测试完成"
            
            # 复制性能报告
            if [ -d "performance-reports" ]; then
                cp -r performance-reports/* "$VALIDATION_REPORT_DIR/" 2>/dev/null || true
            fi
            
            return 0
        else
            log_warning "性能测试遇到问题，但继续验证"
            return 0
        fi
    else
        log_warning "性能测试脚本不存在: $perf_script"
        return 0
    fi
}

# 运行安全扫描
run_security_scan() {
    if [ "$RUN_SECURITY_SCAN" != "true" ]; then
        log_warning "跳过安全扫描"
        return 0
    fi
    
    log_step "运行安全扫描..."
    
    local security_script="../security-hardening/scripts/security-scan.sh"
    
    if [ -f "$security_script" ]; then
        chmod +x "$security_script"
        
        if "$security_script" "$NAMESPACE"; then
            log_success "安全扫描完成"
            
            # 复制安全报告
            if [ -d "security-reports" ]; then
                cp -r security-reports/* "$VALIDATION_REPORT_DIR/" 2>/dev/null || true
            fi
            
            return 0
        else
            log_warning "安全扫描发现问题，请检查安全报告"
            return 0
        fi
    else
        log_warning "安全扫描脚本不存在: $security_script"
        return 0
    fi
}

# 收集系统信息
collect_system_info() {
    log_step "收集系统信息..."
    
    local system_info="${VALIDATION_REPORT_DIR}/system_info_${TIMESTAMP}.txt"
    
    {
        echo "=== 系统信息收集 ==="
        echo "时间戳: $(date)"
        echo ""
        
        echo "=== Kubernetes集群信息 ==="
        kubectl cluster-info
        echo ""
        
        echo "=== 节点信息 ==="
        kubectl get nodes -o wide
        echo ""
        
        echo "=== 命名空间资源 ==="
        kubectl get all -n "$NAMESPACE"
        echo ""
        
        echo "=== Helm发布信息 ==="
        helm list -n "$NAMESPACE"
        echo ""
        
        echo "=== Pod详细信息 ==="
        kubectl describe pods -n "$NAMESPACE"
        echo ""
        
        echo "=== 服务详细信息 ==="
        kubectl describe services -n "$NAMESPACE"
        echo ""
        
        echo "=== Ingress信息 ==="
        kubectl get ingress -n "$NAMESPACE" -o yaml
        echo ""
        
        echo "=== 存储信息 ==="
        kubectl get pv,pvc -n "$NAMESPACE"
        echo ""
        
        echo "=== 配置信息 ==="
        kubectl get configmaps,secrets -n "$NAMESPACE"
        echo ""
        
    } > "$system_info"
    
    log_success "系统信息收集完成: $system_info"
}

# 生成综合验证报告
generate_comprehensive_report() {
    log_step "生成综合验证报告..."
    
    local comprehensive_report="${VALIDATION_REPORT_DIR}/comprehensive_validation_report_${TIMESTAMP}.json"
    
    # 统计验证结果
    local deployment_status="unknown"
    local e2e_status="unknown"
    local performance_status="unknown"
    local security_status="unknown"
    
    # 检查各类报告文件
    if ls "${VALIDATION_REPORT_DIR}"/verification_summary_*.json &> /dev/null; then
        deployment_status="completed"
    fi
    
    if ls "${VALIDATION_REPORT_DIR}"/e2e_test_summary_*.json &> /dev/null; then
        e2e_status="completed"
    fi
    
    if ls "${VALIDATION_REPORT_DIR}"/performance_summary_*.json &> /dev/null; then
        performance_status="completed"
    fi
    
    if ls "${VALIDATION_REPORT_DIR}"/security_scan_*.json &> /dev/null; then
        security_status="completed"
    fi
    
    # 生成综合报告
    cat > "$comprehensive_report" << EOF
{
  "validation_summary": {
    "timestamp": "$TIMESTAMP",
    "namespace": "$NAMESPACE",
    "helm_release": "$HELM_RELEASE",
    "validation_status": "completed"
  },
  "test_results": {
    "deployment_verification": "$deployment_status",
    "e2e_tests": "$e2e_status",
    "performance_tests": "$performance_status",
    "security_scan": "$security_status"
  },
  "configuration": {
    "run_deployment_check": "$RUN_DEPLOYMENT_CHECK",
    "run_e2e_tests": "$RUN_E2E_TESTS",
    "run_performance_tests": "$RUN_PERFORMANCE_TESTS",
    "run_security_scan": "$RUN_SECURITY_SCAN"
  },
  "report_files": [
$(find "$VALIDATION_REPORT_DIR" -name "*.json" -o -name "*.txt" | sed 's/.*/"&"/' | paste -sd ',' -)
  ]
}
EOF
    
    log_success "综合验证报告生成完成: $comprehensive_report"
}

# 显示验证结果摘要
show_validation_summary() {
    log_result "项目验证结果摘要"
    echo ""
    
    echo -e "${CYAN}验证环境:${NC}"
    echo "  命名空间: $NAMESPACE"
    echo "  Helm发布: $HELM_RELEASE"
    echo "  验证时间: $(date)"
    echo ""
    
    echo -e "${CYAN}验证项目:${NC}"
    echo "  🚀 部署验证: $([ "$RUN_DEPLOYMENT_CHECK" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")"
    echo "  🧪 端到端测试: $([ "$RUN_E2E_TESTS" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")"
    echo "  ⚡ 性能测试: $([ "$RUN_PERFORMANCE_TESTS" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")"
    echo "  🔒 安全扫描: $([ "$RUN_SECURITY_SCAN" = "true" ] && echo "✅ 已执行" || echo "⏭️ 跳过")"
    echo ""
    
    echo -e "${CYAN}报告文件:${NC}"
    if [ -d "$VALIDATION_REPORT_DIR" ]; then
        find "$VALIDATION_REPORT_DIR" -type f | head -10 | while read -r file; do
            echo "  📄 $file"
        done
        
        local total_files
        total_files=$(find "$VALIDATION_REPORT_DIR" -type f | wc -l)
        if [ "$total_files" -gt 10 ]; then
            echo "  ... 还有 $((total_files - 10)) 个文件"
        fi
    fi
    echo ""
    
    echo -e "${CYAN}下一步建议:${NC}"
    echo "  1. 查看详细的验证报告文件"
    echo "  2. 根据测试结果优化应用配置"
    echo "  3. 修复发现的安全问题"
    echo "  4. 调整性能参数和资源配置"
    echo "  5. 在生产环境中部署应用"
    echo ""
    
    echo -e "${GREEN}🎉 项目验证完成！${NC}"
    echo -e "${GREEN}您的云原生电商平台已准备就绪！${NC}"
}

# 主函数
main() {
    show_banner
    
    log_info "开始综合项目验证..."
    echo ""
    
    create_validation_report_dir
    check_prerequisites
    
    # 执行各项验证
    local validation_errors=0
    
    if ! run_deployment_verification; then
        validation_errors=$((validation_errors + 1))
    fi
    
    if ! run_e2e_tests; then
        validation_errors=$((validation_errors + 1))
    fi
    
    if ! run_performance_tests; then
        validation_errors=$((validation_errors + 1))
    fi
    
    if ! run_security_scan; then
        validation_errors=$((validation_errors + 1))
    fi
    
    # 收集系统信息和生成报告
    collect_system_info
    generate_comprehensive_report
    
    echo ""
    show_validation_summary
    
    if [ "$validation_errors" -gt 0 ]; then
        log_warning "验证过程中发现 $validation_errors 个问题，请检查详细报告"
        exit 1
    else
        log_success "所有验证项目都已成功完成！"
        exit 0
    fi
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-deployment)
            RUN_DEPLOYMENT_CHECK="false"
            shift
            ;;
        --skip-e2e)
            RUN_E2E_TESTS="false"
            shift
            ;;
        --skip-performance)
            RUN_PERFORMANCE_TESTS="false"
            shift
            ;;
        --skip-security)
            RUN_SECURITY_SCAN="false"
            shift
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --release)
            HELM_RELEASE="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --skip-deployment    跳过部署验证"
            echo "  --skip-e2e          跳过端到端测试"
            echo "  --skip-performance  跳过性能测试"
            echo "  --skip-security     跳过安全扫描"
            echo "  --namespace NAME    指定命名空间 (默认: ecommerce-prod)"
            echo "  --release NAME      指定Helm发布名称 (默认: ecommerce-platform)"
            echo "  -h, --help          显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
