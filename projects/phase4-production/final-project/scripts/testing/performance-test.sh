#!/bin/bash

# 性能测试脚本
# 对云原生电商平台进行负载测试和性能评估

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="${NAMESPACE:-ecommerce-prod}"
HELM_RELEASE="${HELM_RELEASE:-ecommerce-platform}"
BASE_URL="${BASE_URL:-http://localhost}"
PERF_REPORT_DIR="performance-reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 测试参数
CONCURRENT_USERS="${CONCURRENT_USERS:-10}"
TEST_DURATION="${TEST_DURATION:-60}"
REQUESTS_PER_SECOND="${REQUESTS_PER_SECOND:-100}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查性能测试工具
check_performance_tools() {
    log_info "检查性能测试工具..."
    
    local tools=("ab" "curl" "jq")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少以下工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具:"
        log_info "  Ubuntu/Debian: sudo apt-get install apache2-utils curl jq"
        log_info "  CentOS/RHEL: sudo yum install httpd-tools curl jq"
        log_info "  macOS: brew install apache2-utils curl jq"
        exit 1
    fi
    
    log_success "性能测试工具检查完成"
}

# 创建性能报告目录
create_perf_report_dir() {
    mkdir -p "$PERF_REPORT_DIR"
    log_info "创建性能报告目录: $PERF_REPORT_DIR"
}

# 设置端口转发
setup_port_forwarding() {
    log_info "设置服务端口转发..."
    
    # 用户服务
    kubectl port-forward "service/${HELM_RELEASE}-user-service" 3001:3001 -n "$NAMESPACE" &
    USER_SERVICE_PID=$!
    
    # 商品服务
    kubectl port-forward "service/${HELM_RELEASE}-product-service" 3002:3002 -n "$NAMESPACE" &
    PRODUCT_SERVICE_PID=$!
    
    # 订单服务
    kubectl port-forward "service/${HELM_RELEASE}-order-service" 3003:3003 -n "$NAMESPACE" &
    ORDER_SERVICE_PID=$!
    
    # 等待端口转发建立
    sleep 10
    
    log_success "端口转发设置完成"
}

# 清理端口转发
cleanup_port_forwarding() {
    log_info "清理端口转发..."
    
    for pid in $USER_SERVICE_PID $PRODUCT_SERVICE_PID $ORDER_SERVICE_PID; do
        if [ -n "$pid" ]; then
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    log_success "端口转发清理完成"
}

# Apache Bench负载测试
run_ab_test() {
    local service_name="$1"
    local port="$2"
    local endpoint="$3"
    local test_name="$4"
    
    log_info "运行Apache Bench测试: $test_name"
    
    local ab_report="${PERF_REPORT_DIR}/ab_${service_name}_${test_name}_${TIMESTAMP}.txt"
    local url="$BASE_URL:$port$endpoint"
    
    # 运行Apache Bench测试
    ab -n "$REQUESTS_PER_SECOND" -c "$CONCURRENT_USERS" -g "${ab_report}.gnuplot" "$url" > "$ab_report" 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "$test_name 测试完成"
        
        # 提取关键指标
        local rps
        rps=$(grep "Requests per second" "$ab_report" | awk '{print $4}')
        
        local mean_time
        mean_time=$(grep "Time per request" "$ab_report" | head -1 | awk '{print $4}')
        
        local failed_requests
        failed_requests=$(grep "Failed requests" "$ab_report" | awk '{print $3}')
        
        log_info "  RPS: $rps"
        log_info "  平均响应时间: ${mean_time}ms"
        log_info "  失败请求: $failed_requests"
        
        # 保存结构化结果
        cat > "${PERF_REPORT_DIR}/ab_${service_name}_${test_name}_${TIMESTAMP}.json" << EOF
{
  "test_name": "$test_name",
  "service": "$service_name",
  "url": "$url",
  "concurrent_users": $CONCURRENT_USERS,
  "total_requests": $REQUESTS_PER_SECOND,
  "requests_per_second": "$rps",
  "mean_response_time_ms": "$mean_time",
  "failed_requests": "$failed_requests",
  "timestamp": "$TIMESTAMP"
}
EOF
    else
        log_error "$test_name 测试失败"
    fi
}

# 用户服务性能测试
test_user_service_performance() {
    log_info "测试用户服务性能..."
    
    # 健康检查端点测试
    run_ab_test "user-service" "3001" "/health" "health_check"
    
    # 用户列表端点测试（如果存在）
    run_ab_test "user-service" "3001" "/api/users" "user_list"
    
    log_success "用户服务性能测试完成"
}

# 商品服务性能测试
test_product_service_performance() {
    log_info "测试商品服务性能..."
    
    # 健康检查端点测试
    run_ab_test "product-service" "3002" "/health" "health_check"
    
    # 商品列表端点测试
    run_ab_test "product-service" "3002" "/api/v1/products" "product_list"
    
    # 商品搜索端点测试
    run_ab_test "product-service" "3002" "/api/v1/products/search?q=test" "product_search"
    
    log_success "商品服务性能测试完成"
}

# 订单服务性能测试
test_order_service_performance() {
    log_info "测试订单服务性能..."
    
    # 健康检查端点测试
    run_ab_test "order-service" "3003" "/health" "health_check"
    
    log_success "订单服务性能测试完成"
}

# 并发用户模拟测试
run_concurrent_user_test() {
    log_info "运行并发用户模拟测试..."
    
    local concurrent_test_report="${PERF_REPORT_DIR}/concurrent_users_${TIMESTAMP}.txt"
    local pids=()
    
    # 启动多个并发进程
    for i in $(seq 1 "$CONCURRENT_USERS"); do
        {
            local user_id="user_$i"
            local start_time=$(date +%s.%N)
            
            # 模拟用户行为：浏览商品 -> 查看详情 -> 搜索
            curl -s "$BASE_URL:3002/api/v1/products" > /dev/null
            curl -s "$BASE_URL:3002/api/v1/products/1" > /dev/null
            curl -s "$BASE_URL:3002/api/v1/products/search?q=test" > /dev/null
            
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc)
            
            echo "$user_id,$duration" >> "$concurrent_test_report"
        } &
        pids+=($!)
    done
    
    # 等待所有进程完成
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    # 分析结果
    if [ -f "$concurrent_test_report" ]; then
        local avg_duration
        avg_duration=$(awk -F',' '{sum+=$2; count++} END {print sum/count}' "$concurrent_test_report")
        
        local max_duration
        max_duration=$(awk -F',' '{if($2>max) max=$2} END {print max}' "$concurrent_test_report")
        
        local min_duration
        min_duration=$(awk -F',' '{if(NR==1 || $2<min) min=$2} END {print min}' "$concurrent_test_report")
        
        log_success "并发用户测试完成"
        log_info "  并发用户数: $CONCURRENT_USERS"
        log_info "  平均响应时间: ${avg_duration}s"
        log_info "  最大响应时间: ${max_duration}s"
        log_info "  最小响应时间: ${min_duration}s"
        
        # 保存结构化结果
        cat > "${PERF_REPORT_DIR}/concurrent_users_${TIMESTAMP}.json" << EOF
{
  "test_type": "concurrent_users",
  "concurrent_users": $CONCURRENT_USERS,
  "average_duration": "$avg_duration",
  "max_duration": "$max_duration",
  "min_duration": "$min_duration",
  "timestamp": "$TIMESTAMP"
}
EOF
    fi
}

# 资源使用情况监控
monitor_resource_usage() {
    log_info "监控资源使用情况..."
    
    local resource_report="${PERF_REPORT_DIR}/resource_usage_${TIMESTAMP}.json"
    
    # 获取Pod资源使用情况
    kubectl top pods -n "$NAMESPACE" --no-headers > "${PERF_REPORT_DIR}/pod_resources_${TIMESTAMP}.txt" 2>/dev/null || {
        log_warning "无法获取Pod资源使用情况（可能需要安装metrics-server）"
        return 0
    }
    
    # 获取节点资源使用情况
    kubectl top nodes --no-headers > "${PERF_REPORT_DIR}/node_resources_${TIMESTAMP}.txt" 2>/dev/null || {
        log_warning "无法获取节点资源使用情况"
    }
    
    # 分析Pod资源使用
    if [ -f "${PERF_REPORT_DIR}/pod_resources_${TIMESTAMP}.txt" ]; then
        local total_cpu=0
        local total_memory=0
        local pod_count=0
        
        while read -r line; do
            local cpu=$(echo "$line" | awk '{print $2}' | sed 's/m$//')
            local memory=$(echo "$line" | awk '{print $3}' | sed 's/Mi$//')
            
            if [[ "$cpu" =~ ^[0-9]+$ ]]; then
                total_cpu=$((total_cpu + cpu))
            fi
            
            if [[ "$memory" =~ ^[0-9]+$ ]]; then
                total_memory=$((total_memory + memory))
            fi
            
            pod_count=$((pod_count + 1))
        done < "${PERF_REPORT_DIR}/pod_resources_${TIMESTAMP}.txt"
        
        local avg_cpu=$((total_cpu / pod_count))
        local avg_memory=$((total_memory / pod_count))
        
        log_success "资源使用情况监控完成"
        log_info "  Pod总数: $pod_count"
        log_info "  平均CPU使用: ${avg_cpu}m"
        log_info "  平均内存使用: ${avg_memory}Mi"
        
        # 保存结构化结果
        cat > "$resource_report" << EOF
{
  "pod_count": $pod_count,
  "total_cpu_millicores": $total_cpu,
  "total_memory_mi": $total_memory,
  "average_cpu_millicores": $avg_cpu,
  "average_memory_mi": $avg_memory,
  "timestamp": "$TIMESTAMP"
}
EOF
    fi
}

# 生成性能测试报告
generate_performance_report() {
    log_info "生成性能测试报告..."
    
    local summary_report="${PERF_REPORT_DIR}/performance_summary_${TIMESTAMP}.json"
    
    # 收集所有测试结果
    local test_results=()
    
    for json_file in "${PERF_REPORT_DIR}"/*_"${TIMESTAMP}".json; do
        if [ -f "$json_file" ]; then
            test_results+=("$(cat "$json_file")")
        fi
    done
    
    # 生成汇总报告
    cat > "$summary_report" << EOF
{
  "timestamp": "$TIMESTAMP",
  "test_configuration": {
    "namespace": "$NAMESPACE",
    "helm_release": "$HELM_RELEASE",
    "base_url": "$BASE_URL",
    "concurrent_users": $CONCURRENT_USERS,
    "test_duration": $TEST_DURATION,
    "requests_per_second": $REQUESTS_PER_SECOND
  },
  "test_results": [
$(IFS=','; echo "${test_results[*]}")
  ]
}
EOF
    
    log_success "性能测试报告生成完成: $summary_report"
}

# 显示性能测试结果
show_performance_results() {
    log_info "性能测试结果摘要:"
    echo ""
    
    echo "测试配置:"
    echo "  并发用户数: $CONCURRENT_USERS"
    echo "  测试持续时间: ${TEST_DURATION}s"
    echo "  每秒请求数: $REQUESTS_PER_SECOND"
    echo ""
    
    echo "测试结果文件:"
    ls -la "$PERF_REPORT_DIR"/*_"${TIMESTAMP}".* 2>/dev/null || echo "  无测试结果文件"
    echo ""
    
    echo "性能建议:"
    echo "  1. 检查响应时间是否在可接受范围内（通常 < 200ms）"
    echo "  2. 确认错误率低于 1%"
    echo "  3. 监控资源使用情况，确保有足够的余量"
    echo "  4. 根据实际负载调整副本数和资源限制"
    echo ""
    
    echo "性能报告位置: $PERF_REPORT_DIR"
}

# 主函数
main() {
    log_info "开始性能测试..."
    
    # 设置清理陷阱
    trap cleanup_port_forwarding EXIT
    
    check_performance_tools
    create_perf_report_dir
    setup_port_forwarding
    
    # 执行性能测试
    test_user_service_performance
    test_product_service_performance
    test_order_service_performance
    run_concurrent_user_test
    monitor_resource_usage
    
    # 生成报告
    generate_performance_report
    show_performance_results
    
    log_success "性能测试完成！"
}

# 执行主函数
main "$@"
