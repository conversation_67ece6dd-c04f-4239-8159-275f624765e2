#!/bin/bash

# 健康检查脚本
# 检查所有服务的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查基础设施服务
check_infrastructure() {
    echo "=========================================="
    echo "         基础设施服务健康检查"
    echo "=========================================="
    echo ""
    
    # 检查MariaDB
    log_info "检查MariaDB..."
    if docker exec ecommerce-mariadb mysqladmin ping -h localhost --silent > /dev/null 2>&1; then
        log_success "MariaDB运行正常"
        
        # 检查数据库连接
        if docker exec ecommerce-mariadb mysql -uecommerce -ppassword -e "SELECT 1" > /dev/null 2>&1; then
            log_success "MariaDB应用用户连接正常"
        else
            log_error "MariaDB应用用户连接失败"
        fi
        
        # 检查数据库
        db_count=$(docker exec ecommerce-mariadb mysql -uecommerce -ppassword -e "SHOW DATABASES LIKE 'ecommerce_%'" 2>/dev/null | wc -l)
        if [ "$db_count" -gt 0 ]; then
            log_success "应用数据库存在 ($db_count 个)"
        else
            log_warning "应用数据库不存在"
        fi
    else
        log_error "MariaDB连接失败"
    fi
    echo ""
    
    # 检查Redis
    log_info "检查Redis..."
    if docker exec ecommerce-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
        log_success "Redis运行正常"
        
        # 检查Redis信息
        redis_info=$(docker exec ecommerce-redis redis-cli info server 2>/dev/null | grep "redis_version" || echo "unknown")
        log_info "Redis版本: $redis_info"
    else
        log_error "Redis连接失败"
    fi
    echo ""
    
    # 检查RabbitMQ
    log_info "检查RabbitMQ..."
    if docker exec ecommerce-rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
        log_success "RabbitMQ运行正常"
        
        # 检查RabbitMQ状态
        if docker exec ecommerce-rabbitmq rabbitmq-diagnostics status > /dev/null 2>&1; then
            log_success "RabbitMQ状态正常"
        else
            log_warning "RabbitMQ状态检查失败"
        fi
        
        # 检查管理界面
        if curl -s -f -u admin:password http://localhost:15672/api/overview > /dev/null 2>&1; then
            log_success "RabbitMQ管理界面可访问"
        else
            log_warning "RabbitMQ管理界面不可访问"
        fi
    else
        log_error "RabbitMQ连接失败"
    fi
    echo ""
}

# 检查应用服务
check_application_services() {
    echo "=========================================="
    echo "         应用服务健康检查"
    echo "=========================================="
    echo ""
    
    # 检查用户服务
    log_info "检查用户服务..."
    if curl -s -f http://localhost:8081/api/users/health > /dev/null 2>&1; then
        log_success "用户服务运行正常"
        
        # 获取健康检查详情
        health_response=$(curl -s http://localhost:8081/api/users/health 2>/dev/null || echo "{}")
        echo "  响应: $health_response"
        
        # 检查actuator健康端点
        if curl -s -f http://localhost:8081/actuator/health > /dev/null 2>&1; then
            actuator_health=$(curl -s http://localhost:8081/actuator/health 2>/dev/null || echo "{}")
            log_success "Actuator健康检查正常"
            echo "  Actuator状态: $actuator_health"
        else
            log_warning "Actuator健康检查不可访问"
        fi
    else
        log_error "用户服务不可访问"
        log_info "检查用户服务是否已启动..."
        
        # 检查端口是否被占用
        if netstat -an 2>/dev/null | grep -q ":8081"; then
            log_warning "端口8081被占用，但服务不响应"
        else
            log_warning "端口8081未被占用，服务可能未启动"
        fi
    fi
    echo ""
}

# 检查网络连接
check_network() {
    echo "=========================================="
    echo "         网络连接检查"
    echo "=========================================="
    echo ""
    
    # 检查端口占用
    log_info "检查端口占用情况..."
    
    ports=("3306:MariaDB" "6379:Redis" "5672:RabbitMQ" "15672:RabbitMQ管理" "8081:用户服务")
    
    for port_info in "${ports[@]}"; do
        port=$(echo "$port_info" | cut -d: -f1)
        service=$(echo "$port_info" | cut -d: -f2)
        
        if netstat -an 2>/dev/null | grep -q ":$port "; then
            log_success "$service (端口 $port) 正在监听"
        else
            log_warning "$service (端口 $port) 未在监听"
        fi
    done
    echo ""
    
    # 检查容器网络
    log_info "检查容器网络..."
    if docker network ls | grep -q "ecommerce"; then
        log_success "ecommerce网络存在"
    else
        log_warning "ecommerce网络不存在"
    fi
    echo ""
}

# 检查容器状态
check_containers() {
    echo "=========================================="
    echo "         容器状态检查"
    echo "=========================================="
    echo ""
    
    log_info "检查容器运行状态..."
    
    # 检查基础设施容器
    containers=("ecommerce-mariadb" "ecommerce-redis" "ecommerce-rabbitmq")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            status=$(docker inspect --format='{{.State.Status}}' "$container" 2>/dev/null || echo "unknown")
            if [ "$status" = "running" ]; then
                log_success "$container 运行正常"
            else
                log_error "$container 状态异常: $status"
            fi
        else
            log_error "$container 未运行"
        fi
    done
    echo ""
    
    # 显示所有相关容器
    log_info "所有相关容器状态:"
    docker ps -a --filter "name=ecommerce" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || log_warning "无法获取容器状态"
    echo ""
}

# 生成健康报告
generate_health_report() {
    echo "=========================================="
    echo "         健康检查报告"
    echo "=========================================="
    echo ""
    
    # 统计服务状态
    total_services=0
    healthy_services=0
    
    # 检查基础设施
    services=("MariaDB:3306" "Redis:6379" "RabbitMQ:5672")
    
    for service_info in "${services[@]}"; do
        service=$(echo "$service_info" | cut -d: -f1)
        port=$(echo "$service_info" | cut -d: -f2)
        total_services=$((total_services + 1))
        
        if netstat -an 2>/dev/null | grep -q ":$port "; then
            healthy_services=$((healthy_services + 1))
            echo "✅ $service"
        else
            echo "❌ $service"
        fi
    done
    
    # 检查应用服务
    total_services=$((total_services + 1))
    if curl -s -f http://localhost:8081/api/users/health > /dev/null 2>&1; then
        healthy_services=$((healthy_services + 1))
        echo "✅ 用户服务"
    else
        echo "❌ 用户服务"
    fi
    
    echo ""
    echo "总体健康状态: $healthy_services/$total_services 服务正常"
    
    if [ "$healthy_services" -eq "$total_services" ]; then
        log_success "所有服务运行正常！"
        return 0
    else
        log_warning "部分服务存在问题"
        return 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "         系统健康检查"
    echo "=========================================="
    echo ""
    
    # 执行各项检查
    check_containers
    check_infrastructure
    check_network
    check_application_services
    
    # 生成报告
    generate_health_report
    
    echo ""
    echo "健康检查完成！"
}

# 执行主函数
main "$@"
