#!/bin/bash

# 用户服务测试脚本
# 测试用户服务的核心功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8081/api/users"
TEST_USER_USERNAME="testuser$(date +%s)"
TEST_USER_EMAIL="test$(date +%s)@example.com"
TEST_USER_PASSWORD="Password123"
TEST_USER_PHONE="138$(date +%s | tail -c 9)"
ACCESS_TOKEN=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否启动
check_service() {
    log_info "检查用户服务是否启动..."
    
    for i in {1..30}; do
        if curl -s -f "$BASE_URL/health" > /dev/null 2>&1; then
            log_success "用户服务已启动"
            return 0
        fi
        log_info "等待服务启动... ($i/30)"
        sleep 2
    done
    
    log_error "用户服务启动超时"
    return 1
}

# 测试健康检查
test_health_check() {
    log_info "测试健康检查接口..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/health")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "健康检查通过"
        echo "响应: ${response%???}"
    else
        log_error "健康检查失败，HTTP状态码: $http_code"
        return 1
    fi
}

# 测试用户注册
test_user_registration() {
    log_info "测试用户注册..."
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER_USERNAME\",
            \"email\": \"$TEST_USER_EMAIL\",
            \"password\": \"$TEST_USER_PASSWORD\",
            \"confirmPassword\": \"$TEST_USER_PASSWORD\",
            \"phone\": \"$TEST_USER_PHONE\"
        }" \
        "$BASE_URL/auth/register")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "201" ]; then
        log_success "用户注册成功"
        echo "响应: $response_body"
    else
        log_error "用户注册失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试用户登录
test_user_login() {
    log_info "测试用户登录..."
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"usernameOrEmail\": \"$TEST_USER_USERNAME\",
            \"password\": \"$TEST_USER_PASSWORD\"
        }" \
        "$BASE_URL/auth/login")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "用户登录成功"
        # 提取访问令牌
        ACCESS_TOKEN=$(echo "$response_body" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$ACCESS_TOKEN" ]; then
            log_success "获取到访问令牌: ${ACCESS_TOKEN:0:20}..."
        else
            log_warning "未能提取访问令牌"
        fi
        echo "响应: $response_body"
    else
        log_error "用户登录失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取当前用户信息
test_get_current_user() {
    log_info "测试获取当前用户信息..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/me")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取用户信息成功"
        echo "响应: $response_body"
    else
        log_error "获取用户信息失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试检查用户名可用性
test_check_username() {
    log_info "测试检查用户名可用性..."
    
    # 测试已存在的用户名
    response=$(curl -s -w "%{http_code}" \
        "$BASE_URL/public/check-username?username=$TEST_USER_USERNAME")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "检查用户名可用性成功"
        echo "响应: $response_body"
        
        # 检查返回的available字段应该为false
        if echo "$response_body" | grep -q '"available":false'; then
            log_success "用户名可用性检查正确（已存在用户名返回false）"
        else
            log_warning "用户名可用性检查结果异常"
        fi
    else
        log_error "检查用户名可用性失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
    
    # 测试不存在的用户名
    new_username="newuser$(date +%s)"
    response=$(curl -s -w "%{http_code}" \
        "$BASE_URL/public/check-username?username=$new_username")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        if echo "$response_body" | grep -q '"available":true'; then
            log_success "新用户名可用性检查正确（不存在用户名返回true）"
        else
            log_warning "新用户名可用性检查结果异常"
        fi
    fi
}

# 测试更新用户详细信息
test_update_user_profile() {
    log_info "测试更新用户详细信息..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{
            \"firstName\": \"测试\",
            \"lastName\": \"用户\",
            \"bio\": \"这是一个测试用户的个人简介\",
            \"gender\": \"MALE\"
        }" \
        "$BASE_URL/me/profile")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "更新用户详细信息成功"
        echo "响应: $response_body"
    else
        log_error "更新用户详细信息失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试添加用户地址
test_add_user_address() {
    log_info "测试添加用户地址..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{
            \"name\": \"张三\",
            \"phone\": \"13900139000\",
            \"province\": \"北京市\",
            \"city\": \"北京市\",
            \"district\": \"朝阳区\",
            \"detailAddress\": \"某某街道123号\",
            \"postalCode\": \"100000\",
            \"isDefault\": true
        }" \
        "$BASE_URL/me/addresses")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "201" ]; then
        log_success "添加用户地址成功"
        echo "响应: $response_body"
    else
        log_error "添加用户地址失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取用户地址列表
test_get_user_addresses() {
    log_info "测试获取用户地址列表..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/me/addresses")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取用户地址列表成功"
        echo "响应: $response_body"
    else
        log_error "获取用户地址列表失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 主测试函数
main() {
    echo "=========================================="
    echo "         用户服务功能测试"
    echo "=========================================="
    echo ""
    
    # 检查服务状态
    if ! check_service; then
        log_error "服务未启动，请先启动用户服务"
        exit 1
    fi
    
    echo ""
    echo "开始执行测试用例..."
    echo ""
    
    # 执行测试用例
    test_health_check
    echo ""
    
    test_user_registration
    echo ""
    
    test_user_login
    echo ""
    
    test_get_current_user
    echo ""
    
    test_check_username
    echo ""
    
    test_update_user_profile
    echo ""
    
    test_add_user_address
    echo ""
    
    test_get_user_addresses
    echo ""
    
    echo "=========================================="
    log_success "所有测试用例执行完成！"
    echo "=========================================="
}

# 执行主函数
main "$@"
