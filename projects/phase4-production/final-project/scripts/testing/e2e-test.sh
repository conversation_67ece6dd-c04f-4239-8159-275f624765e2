#!/bin/bash

# 端到端测试脚本
# 验证云原生电商平台的完整业务流程

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="${NAMESPACE:-ecommerce-prod}"
HELM_RELEASE="${HELM_RELEASE:-ecommerce-platform}"
BASE_URL="${BASE_URL:-http://localhost}"
TEST_REPORT_DIR="e2e-test-reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 测试数据
TEST_USER_EMAIL="<EMAIL>"
TEST_USER_PASSWORD="TestPassword123!"
TEST_PRODUCT_NAME="测试商品"
TEST_PRODUCT_PRICE="99.99"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建测试报告目录
create_test_report_dir() {
    mkdir -p "$TEST_REPORT_DIR"
    log_info "创建测试报告目录: $TEST_REPORT_DIR"
}

# 设置端口转发
setup_port_forwarding() {
    log_info "设置服务端口转发..."
    
    # 用户服务
    kubectl port-forward "service/${HELM_RELEASE}-user-service" 3001:3001 -n "$NAMESPACE" &
    USER_SERVICE_PID=$!
    
    # 商品服务
    kubectl port-forward "service/${HELM_RELEASE}-product-service" 3002:3002 -n "$NAMESPACE" &
    PRODUCT_SERVICE_PID=$!
    
    # 订单服务
    kubectl port-forward "service/${HELM_RELEASE}-order-service" 3003:3003 -n "$NAMESPACE" &
    ORDER_SERVICE_PID=$!
    
    # 支付服务
    kubectl port-forward "service/${HELM_RELEASE}-payment-service" 3004:3004 -n "$NAMESPACE" &
    PAYMENT_SERVICE_PID=$!
    
    # 等待端口转发建立
    sleep 10
    
    log_success "端口转发设置完成"
}

# 清理端口转发
cleanup_port_forwarding() {
    log_info "清理端口转发..."
    
    for pid in $USER_SERVICE_PID $PRODUCT_SERVICE_PID $ORDER_SERVICE_PID $PAYMENT_SERVICE_PID; do
        if [ -n "$pid" ]; then
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    log_success "端口转发清理完成"
}

# 测试服务健康状态
test_health_endpoints() {
    log_info "测试服务健康状态..."
    
    local services=("user-service:3001" "product-service:3002" "order-service:3003" "payment-service:3004")
    local health_results=()
    
    for service_port in "${services[@]}"; do
        local service="${service_port%:*}"
        local port="${service_port#*:}"
        
        log_info "测试 $service 健康端点..."
        
        local response
        local status_code
        
        if response=$(curl -s -w "%{http_code}" "$BASE_URL:$port/health" 2>/dev/null); then
            status_code="${response: -3}"
            response_body="${response%???}"
            
            if [ "$status_code" = "200" ]; then
                log_success "$service 健康检查通过"
                health_results+=("$service:PASS")
            else
                log_error "$service 健康检查失败 (状态码: $status_code)"
                health_results+=("$service:FAIL")
            fi
        else
            log_error "$service 健康检查失败 (连接错误)"
            health_results+=("$service:ERROR")
        fi
    done
    
    # 保存健康检查结果
    printf '%s\n' "${health_results[@]}" > "${TEST_REPORT_DIR}/health_check_${TIMESTAMP}.txt"
}

# 测试用户注册
test_user_registration() {
    log_info "测试用户注册..."
    
    local response
    local status_code
    
    # 构造注册请求
    local user_data='{
        "email": "'$TEST_USER_EMAIL'",
        "password": "'$TEST_USER_PASSWORD'",
        "firstName": "测试",
        "lastName": "用户",
        "phone": "13800138000"
    }'
    
    if response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$user_data" \
        "$BASE_URL:3001/api/auth/register" 2>/dev/null); then
        
        status_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$status_code" = "201" ] || [ "$status_code" = "200" ]; then
            log_success "用户注册成功"
            echo "$response_body" > "${TEST_REPORT_DIR}/user_registration_${TIMESTAMP}.json"
            return 0
        elif [ "$status_code" = "409" ]; then
            log_warning "用户已存在，继续测试"
            return 0
        else
            log_error "用户注册失败 (状态码: $status_code)"
            echo "$response_body" > "${TEST_REPORT_DIR}/user_registration_error_${TIMESTAMP}.txt"
            return 1
        fi
    else
        log_error "用户注册请求失败"
        return 1
    fi
}

# 测试用户登录
test_user_login() {
    log_info "测试用户登录..."
    
    local response
    local status_code
    
    # 构造登录请求
    local login_data='{
        "email": "'$TEST_USER_EMAIL'",
        "password": "'$TEST_USER_PASSWORD'"
    }'
    
    if response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$BASE_URL:3001/api/auth/login" 2>/dev/null); then
        
        status_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$status_code" = "200" ]; then
            log_success "用户登录成功"
            
            # 提取JWT令牌
            JWT_TOKEN=$(echo "$response_body" | jq -r '.token // .accessToken // empty' 2>/dev/null || echo "")
            
            if [ -n "$JWT_TOKEN" ] && [ "$JWT_TOKEN" != "null" ]; then
                log_success "JWT令牌获取成功"
                echo "$response_body" > "${TEST_REPORT_DIR}/user_login_${TIMESTAMP}.json"
                return 0
            else
                log_warning "JWT令牌提取失败，但登录成功"
                return 0
            fi
        else
            log_error "用户登录失败 (状态码: $status_code)"
            echo "$response_body" > "${TEST_REPORT_DIR}/user_login_error_${TIMESTAMP}.txt"
            return 1
        fi
    else
        log_error "用户登录请求失败"
        return 1
    fi
}

# 测试商品列表
test_product_list() {
    log_info "测试商品列表..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" \
        "$BASE_URL:3002/api/v1/products" 2>/dev/null); then
        
        status_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$status_code" = "200" ]; then
            log_success "商品列表获取成功"
            echo "$response_body" > "${TEST_REPORT_DIR}/product_list_${TIMESTAMP}.json"
            
            # 检查是否有商品数据
            local product_count
            product_count=$(echo "$response_body" | jq '. | length // 0' 2>/dev/null || echo "0")
            log_info "商品数量: $product_count"
            
            return 0
        else
            log_error "商品列表获取失败 (状态码: $status_code)"
            return 1
        fi
    else
        log_error "商品列表请求失败"
        return 1
    fi
}

# 测试创建商品（需要认证）
test_create_product() {
    log_info "测试创建商品..."
    
    if [ -z "$JWT_TOKEN" ]; then
        log_warning "无JWT令牌，跳过创建商品测试"
        return 0
    fi
    
    local response
    local status_code
    
    # 构造商品数据
    local product_data='{
        "name": "'$TEST_PRODUCT_NAME'",
        "description": "这是一个测试商品",
        "price": '$TEST_PRODUCT_PRICE',
        "category": "测试分类",
        "inventory": 100,
        "sku": "TEST-SKU-001"
    }'
    
    if response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -d "$product_data" \
        "$BASE_URL:3002/api/v1/products" 2>/dev/null); then
        
        status_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$status_code" = "201" ] || [ "$status_code" = "200" ]; then
            log_success "商品创建成功"
            echo "$response_body" > "${TEST_REPORT_DIR}/product_create_${TIMESTAMP}.json"
            
            # 提取商品ID
            PRODUCT_ID=$(echo "$response_body" | jq -r '.id // .productId // empty' 2>/dev/null || echo "")
            
            return 0
        else
            log_error "商品创建失败 (状态码: $status_code)"
            echo "$response_body" > "${TEST_REPORT_DIR}/product_create_error_${TIMESTAMP}.txt"
            return 1
        fi
    else
        log_error "商品创建请求失败"
        return 1
    fi
}

# 测试订单创建
test_create_order() {
    log_info "测试创建订单..."
    
    if [ -z "$JWT_TOKEN" ]; then
        log_warning "无JWT令牌，跳过创建订单测试"
        return 0
    fi
    
    local response
    local status_code
    
    # 构造订单数据
    local order_data='{
        "items": [
            {
                "productId": "'${PRODUCT_ID:-1}'",
                "quantity": 2,
                "price": '$TEST_PRODUCT_PRICE'
            }
        ],
        "shippingAddress": {
            "street": "测试街道123号",
            "city": "测试城市",
            "state": "测试省份",
            "zipCode": "100000",
            "country": "中国"
        }
    }'
    
    if response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -d "$order_data" \
        "$BASE_URL:3003/api/v1/orders" 2>/dev/null); then
        
        status_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$status_code" = "201" ] || [ "$status_code" = "200" ]; then
            log_success "订单创建成功"
            echo "$response_body" > "${TEST_REPORT_DIR}/order_create_${TIMESTAMP}.json"
            
            # 提取订单ID
            ORDER_ID=$(echo "$response_body" | jq -r '.id // .orderId // empty' 2>/dev/null || echo "")
            
            return 0
        else
            log_error "订单创建失败 (状态码: $status_code)"
            echo "$response_body" > "${TEST_REPORT_DIR}/order_create_error_${TIMESTAMP}.txt"
            return 1
        fi
    else
        log_error "订单创建请求失败"
        return 1
    fi
}

# 测试支付处理
test_payment_processing() {
    log_info "测试支付处理..."
    
    if [ -z "$JWT_TOKEN" ] || [ -z "$ORDER_ID" ]; then
        log_warning "缺少JWT令牌或订单ID，跳过支付测试"
        return 0
    fi
    
    local response
    local status_code
    
    # 构造支付数据
    local payment_data='{
        "orderId": "'$ORDER_ID'",
        "amount": '$(echo "$TEST_PRODUCT_PRICE * 2" | bc)',
        "paymentMethod": "credit_card",
        "cardInfo": {
            "cardNumber": "****************",
            "expiryMonth": "12",
            "expiryYear": "2025",
            "cvv": "123"
        }
    }'
    
    if response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -d "$payment_data" \
        "$BASE_URL:3004/api/v1/payments" 2>/dev/null); then
        
        status_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$status_code" = "201" ] || [ "$status_code" = "200" ]; then
            log_success "支付处理成功"
            echo "$response_body" > "${TEST_REPORT_DIR}/payment_process_${TIMESTAMP}.json"
            return 0
        else
            log_error "支付处理失败 (状态码: $status_code)"
            echo "$response_body" > "${TEST_REPORT_DIR}/payment_process_error_${TIMESTAMP}.txt"
            return 1
        fi
    else
        log_error "支付处理请求失败"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    log_info "生成端到端测试报告..."
    
    local test_summary="${TEST_REPORT_DIR}/e2e_test_summary_${TIMESTAMP}.json"
    
    cat > "$test_summary" << EOF
{
  "timestamp": "$TIMESTAMP",
  "test_environment": {
    "namespace": "$NAMESPACE",
    "helm_release": "$HELM_RELEASE",
    "base_url": "$BASE_URL"
  },
  "test_results": {
    "health_check": "$([ -f "${TEST_REPORT_DIR}/health_check_${TIMESTAMP}.txt" ] && echo "completed" || echo "failed")",
    "user_registration": "$([ -f "${TEST_REPORT_DIR}/user_registration_${TIMESTAMP}.json" ] && echo "passed" || echo "failed")",
    "user_login": "$([ -f "${TEST_REPORT_DIR}/user_login_${TIMESTAMP}.json" ] && echo "passed" || echo "failed")",
    "product_list": "$([ -f "${TEST_REPORT_DIR}/product_list_${TIMESTAMP}.json" ] && echo "passed" || echo "failed")",
    "product_create": "$([ -f "${TEST_REPORT_DIR}/product_create_${TIMESTAMP}.json" ] && echo "passed" || echo "skipped")",
    "order_create": "$([ -f "${TEST_REPORT_DIR}/order_create_${TIMESTAMP}.json" ] && echo "passed" || echo "skipped")",
    "payment_process": "$([ -f "${TEST_REPORT_DIR}/payment_process_${TIMESTAMP}.json" ] && echo "passed" || echo "skipped")"
  },
  "test_data": {
    "test_user_email": "$TEST_USER_EMAIL",
    "test_product_name": "$TEST_PRODUCT_NAME",
    "jwt_token_obtained": "$([ -n "$JWT_TOKEN" ] && echo "true" || echo "false")",
    "product_id": "${PRODUCT_ID:-null}",
    "order_id": "${ORDER_ID:-null}"
  }
}
EOF
    
    log_success "端到端测试报告生成完成: $test_summary"
}

# 显示测试结果
show_test_results() {
    log_info "端到端测试结果摘要:"
    echo ""
    
    echo "测试环境:"
    echo "  命名空间: $NAMESPACE"
    echo "  Helm发布: $HELM_RELEASE"
    echo "  基础URL: $BASE_URL"
    echo ""
    
    echo "测试结果:"
    echo "  健康检查: $([ -f "${TEST_REPORT_DIR}/health_check_${TIMESTAMP}.txt" ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  用户注册: $([ -f "${TEST_REPORT_DIR}/user_registration_${TIMESTAMP}.json" ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  用户登录: $([ -f "${TEST_REPORT_DIR}/user_login_${TIMESTAMP}.json" ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  商品列表: $([ -f "${TEST_REPORT_DIR}/product_list_${TIMESTAMP}.json" ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  商品创建: $([ -f "${TEST_REPORT_DIR}/product_create_${TIMESTAMP}.json" ] && echo "✅ 通过" || echo "⏭️ 跳过")"
    echo "  订单创建: $([ -f "${TEST_REPORT_DIR}/order_create_${TIMESTAMP}.json" ] && echo "✅ 通过" || echo "⏭️ 跳过")"
    echo "  支付处理: $([ -f "${TEST_REPORT_DIR}/payment_process_${TIMESTAMP}.json" ] && echo "✅ 通过" || echo "⏭️ 跳过")"
    echo ""
    
    echo "测试报告位置: $TEST_REPORT_DIR"
}

# 主函数
main() {
    log_info "开始端到端测试..."
    
    # 设置清理陷阱
    trap cleanup_port_forwarding EXIT
    
    create_test_report_dir
    setup_port_forwarding
    
    # 执行测试
    test_health_endpoints
    test_user_registration
    test_user_login
    test_product_list
    test_create_product
    test_create_order
    test_payment_processing
    
    # 生成报告
    generate_test_report
    show_test_results
    
    log_success "端到端测试完成！"
}

# 执行主函数
main "$@"
