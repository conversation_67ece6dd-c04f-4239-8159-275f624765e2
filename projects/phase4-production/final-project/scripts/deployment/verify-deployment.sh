#!/bin/bash

# 项目验证脚本
# 验证云原生电商平台的完整部署和功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
NAMESPACE="${NAMESPACE:-ecommerce-prod}"
HELM_RELEASE="${HELM_RELEASE:-ecommerce-platform}"
TIMEOUT="${TIMEOUT:-300}"
REPORT_DIR="verification-reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建报告目录
create_report_dir() {
    mkdir -p "$REPORT_DIR"
    log_info "创建验证报告目录: $REPORT_DIR"
}

# 检查Kubernetes连接
check_k8s_connection() {
    log_info "检查Kubernetes集群连接..."
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    local cluster_info
    cluster_info=$(kubectl cluster-info | head -1)
    log_success "Kubernetes集群连接正常: $cluster_info"
}

# 验证命名空间
verify_namespace() {
    log_info "验证命名空间: $NAMESPACE"
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "命名空间 $NAMESPACE 不存在"
        return 1
    fi
    
    # 检查命名空间标签
    local labels
    labels=$(kubectl get namespace "$NAMESPACE" -o jsonpath='{.metadata.labels}')
    log_success "命名空间 $NAMESPACE 存在，标签: $labels"
}

# 验证Helm发布
verify_helm_release() {
    log_info "验证Helm发布: $HELM_RELEASE"
    
    if ! helm list -n "$NAMESPACE" | grep -q "$HELM_RELEASE"; then
        log_error "Helm发布 $HELM_RELEASE 不存在"
        return 1
    fi
    
    local status
    status=$(helm status "$HELM_RELEASE" -n "$NAMESPACE" -o json | jq -r '.info.status')
    
    if [ "$status" = "deployed" ]; then
        log_success "Helm发布状态: $status"
    else
        log_error "Helm发布状态异常: $status"
        return 1
    fi
}

# 验证Pod状态
verify_pods() {
    log_info "验证Pod状态..."
    
    local pods_report="${REPORT_DIR}/pods_status_${TIMESTAMP}.json"
    kubectl get pods -n "$NAMESPACE" -o json > "$pods_report"
    
    # 检查Pod就绪状态
    local total_pods
    total_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | wc -l)
    
    local ready_pods
    ready_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep -c "Running" || echo "0")
    
    local pending_pods
    pending_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep -c "Pending" || echo "0")
    
    local failed_pods
    failed_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep -E "(Error|CrashLoopBackOff|ImagePullBackOff)" | wc -l || echo "0")
    
    echo "Pod状态统计:" > "${REPORT_DIR}/pod_summary_${TIMESTAMP}.txt"
    echo "  总数: $total_pods" >> "${REPORT_DIR}/pod_summary_${TIMESTAMP}.txt"
    echo "  运行中: $ready_pods" >> "${REPORT_DIR}/pod_summary_${TIMESTAMP}.txt"
    echo "  等待中: $pending_pods" >> "${REPORT_DIR}/pod_summary_${TIMESTAMP}.txt"
    echo "  失败: $failed_pods" >> "${REPORT_DIR}/pod_summary_${TIMESTAMP}.txt"
    
    if [ "$failed_pods" -gt 0 ]; then
        log_error "发现 $failed_pods 个失败的Pod"
        kubectl get pods -n "$NAMESPACE" | grep -E "(Error|CrashLoopBackOff|ImagePullBackOff)"
        return 1
    elif [ "$pending_pods" -gt 0 ]; then
        log_warning "发现 $pending_pods 个等待中的Pod"
        kubectl get pods -n "$NAMESPACE" | grep "Pending"
    fi
    
    log_success "Pod状态验证完成: $ready_pods/$total_pods 运行正常"
}

# 验证服务状态
verify_services() {
    log_info "验证服务状态..."
    
    local services_report="${REPORT_DIR}/services_status_${TIMESTAMP}.json"
    kubectl get services -n "$NAMESPACE" -o json > "$services_report"
    
    local services=("user-service" "product-service" "order-service" "payment-service")
    local failed_services=()
    
    for service in "${services[@]}"; do
        local service_name="${HELM_RELEASE}-${service}"
        
        if kubectl get service "$service_name" -n "$NAMESPACE" &> /dev/null; then
            local endpoints
            endpoints=$(kubectl get endpoints "$service_name" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}' | wc -w)
            
            if [ "$endpoints" -gt 0 ]; then
                log_success "服务 $service_name 正常 ($endpoints 个端点)"
            else
                log_warning "服务 $service_name 无可用端点"
                failed_services+=("$service_name")
            fi
        else
            log_error "服务 $service_name 不存在"
            failed_services+=("$service_name")
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "以下服务存在问题: ${failed_services[*]}"
        return 1
    fi
}

# 验证Ingress配置
verify_ingress() {
    log_info "验证Ingress配置..."
    
    local ingress_report="${REPORT_DIR}/ingress_status_${TIMESTAMP}.json"
    kubectl get ingress -n "$NAMESPACE" -o json > "$ingress_report"
    
    local ingress_count
    ingress_count=$(kubectl get ingress -n "$NAMESPACE" --no-headers | wc -l)
    
    if [ "$ingress_count" -eq 0 ]; then
        log_warning "未发现Ingress配置"
        return 0
    fi
    
    # 检查Ingress状态
    kubectl get ingress -n "$NAMESPACE" --no-headers | while read -r line; do
        local ingress_name
        ingress_name=$(echo "$line" | awk '{print $1}')
        
        local hosts
        hosts=$(echo "$line" | awk '{print $3}')
        
        local address
        address=$(echo "$line" | awk '{print $4}')
        
        if [ -n "$address" ] && [ "$address" != "<pending>" ]; then
            log_success "Ingress $ingress_name 正常: $hosts -> $address"
        else
            log_warning "Ingress $ingress_name 地址待分配: $hosts"
        fi
    done
}

# 健康检查
health_check() {
    log_info "执行应用健康检查..."
    
    local services=("user-service:3001" "product-service:3002" "order-service:3003" "payment-service:3004")
    local health_report="${REPORT_DIR}/health_check_${TIMESTAMP}.json"
    local health_results=()
    
    for service_port in "${services[@]}"; do
        local service="${service_port%:*}"
        local port="${service_port#*:}"
        local service_name="${HELM_RELEASE}-${service}"
        
        log_info "检查服务健康状态: $service"
        
        # 端口转发进行健康检查
        kubectl port-forward "service/$service_name" "$port:$port" -n "$NAMESPACE" &
        local pf_pid=$!
        
        # 等待端口转发建立
        sleep 3
        
        # 执行健康检查
        local health_status="unhealthy"
        local response_time="N/A"
        
        if timeout 10 curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
            health_status="healthy"
            response_time=$(curl -w "%{time_total}" -s -o /dev/null "http://localhost:$port/health" 2>/dev/null || echo "N/A")
        fi
        
        # 清理端口转发
        kill $pf_pid 2>/dev/null || true
        wait $pf_pid 2>/dev/null || true
        
        health_results+=("{\"service\":\"$service\",\"status\":\"$health_status\",\"response_time\":\"$response_time\"}")
        
        if [ "$health_status" = "healthy" ]; then
            log_success "服务 $service 健康检查通过 (响应时间: ${response_time}s)"
        else
            log_error "服务 $service 健康检查失败"
        fi
        
        sleep 2
    done
    
    # 生成健康检查报告
    printf '%s\n' "${health_results[@]}" | jq -s '.' > "$health_report"
}

# 验证数据库连接
verify_database() {
    log_info "验证数据库连接..."
    
    # 检查PostgreSQL
    if kubectl get pod -n "$NAMESPACE" -l app.kubernetes.io/name=postgresql &> /dev/null; then
        log_info "检查PostgreSQL连接..."
        
        local pg_pod
        pg_pod=$(kubectl get pod -n "$NAMESPACE" -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}')
        
        if kubectl exec -n "$NAMESPACE" "$pg_pod" -- pg_isready &> /dev/null; then
            log_success "PostgreSQL连接正常"
        else
            log_error "PostgreSQL连接失败"
            return 1
        fi
    else
        log_warning "未发现PostgreSQL Pod"
    fi
    
    # 检查Redis
    if kubectl get pod -n "$NAMESPACE" -l app.kubernetes.io/name=redis &> /dev/null; then
        log_info "检查Redis连接..."
        
        local redis_pod
        redis_pod=$(kubectl get pod -n "$NAMESPACE" -l app.kubernetes.io/name=redis -o jsonpath='{.items[0].metadata.name}')
        
        if kubectl exec -n "$NAMESPACE" "$redis_pod" -- redis-cli ping | grep -q "PONG"; then
            log_success "Redis连接正常"
        else
            log_error "Redis连接失败"
            return 1
        fi
    else
        log_warning "未发现Redis Pod"
    fi
}

# 验证监控组件
verify_monitoring() {
    log_info "验证监控组件..."
    
    # 检查Prometheus
    if kubectl get pod -n monitoring -l app.kubernetes.io/name=prometheus &> /dev/null; then
        log_success "Prometheus组件存在"
        
        # 检查Prometheus目标
        kubectl port-forward -n monitoring svc/kube-prometheus-stack-prometheus 9090:9090 &
        local pf_pid=$!
        sleep 3
        
        if curl -s "http://localhost:9090/api/v1/targets" | jq -r '.data.activeTargets[].health' | grep -q "up"; then
            log_success "Prometheus目标监控正常"
        else
            log_warning "Prometheus目标监控异常"
        fi
        
        kill $pf_pid 2>/dev/null || true
    else
        log_warning "未发现Prometheus组件"
    fi
    
    # 检查Grafana
    if kubectl get pod -n monitoring -l app.kubernetes.io/name=grafana &> /dev/null; then
        log_success "Grafana组件存在"
    else
        log_warning "未发现Grafana组件"
    fi
}

# 性能基准测试
performance_test() {
    log_info "执行性能基准测试..."
    
    local perf_report="${REPORT_DIR}/performance_test_${TIMESTAMP}.json"
    local results=()
    
    # 简单的负载测试
    if command -v ab &> /dev/null; then
        log_info "使用Apache Bench进行负载测试..."
        
        # 对用户服务进行测试
        kubectl port-forward "service/${HELM_RELEASE}-user-service" 3001:3001 -n "$NAMESPACE" &
        local pf_pid=$!
        sleep 3
        
        local ab_result
        ab_result=$(ab -n 100 -c 10 "http://localhost:3001/health" 2>/dev/null | grep -E "(Requests per second|Time per request)" || echo "测试失败")
        
        results+=("{\"service\":\"user-service\",\"test\":\"apache_bench\",\"result\":\"$ab_result\"}")
        
        kill $pf_pid 2>/dev/null || true
        
        log_success "性能测试完成"
    else
        log_warning "Apache Bench未安装，跳过性能测试"
        results+=("{\"service\":\"all\",\"test\":\"apache_bench\",\"result\":\"skipped - ab not installed\"}")
    fi
    
    printf '%s\n' "${results[@]}" | jq -s '.' > "$perf_report"
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    local summary_report="${REPORT_DIR}/verification_summary_${TIMESTAMP}.json"
    
    cat > "$summary_report" << EOF
{
  "timestamp": "$TIMESTAMP",
  "namespace": "$NAMESPACE",
  "helm_release": "$HELM_RELEASE",
  "verification_status": "completed",
  "reports": {
    "pods": "${REPORT_DIR}/pods_status_${TIMESTAMP}.json",
    "services": "${REPORT_DIR}/services_status_${TIMESTAMP}.json",
    "ingress": "${REPORT_DIR}/ingress_status_${TIMESTAMP}.json",
    "health_check": "${REPORT_DIR}/health_check_${TIMESTAMP}.json",
    "performance": "${REPORT_DIR}/performance_test_${TIMESTAMP}.json"
  },
  "summary": {
    "total_pods": $(kubectl get pods -n "$NAMESPACE" --no-headers | wc -l),
    "running_pods": $(kubectl get pods -n "$NAMESPACE" --no-headers | grep -c "Running" || echo "0"),
    "total_services": $(kubectl get services -n "$NAMESPACE" --no-headers | wc -l),
    "ingress_count": $(kubectl get ingress -n "$NAMESPACE" --no-headers | wc -l)
  }
}
EOF
    
    log_success "验证报告生成完成: $summary_report"
}

# 显示验证结果
show_verification_results() {
    log_info "验证结果摘要:"
    echo ""
    
    # 显示基本信息
    echo "集群信息:"
    kubectl cluster-info | head -1
    echo ""
    
    echo "命名空间: $NAMESPACE"
    echo "Helm发布: $HELM_RELEASE"
    echo ""
    
    # 显示资源统计
    echo "资源统计:"
    echo "  Pod总数: $(kubectl get pods -n "$NAMESPACE" --no-headers | wc -l)"
    echo "  运行中Pod: $(kubectl get pods -n "$NAMESPACE" --no-headers | grep -c "Running" || echo "0")"
    echo "  服务总数: $(kubectl get services -n "$NAMESPACE" --no-headers | wc -l)"
    echo "  Ingress总数: $(kubectl get ingress -n "$NAMESPACE" --no-headers | wc -l)"
    echo ""
    
    # 显示访问信息
    echo "访问信息:"
    if kubectl get ingress -n "$NAMESPACE" &> /dev/null; then
        kubectl get ingress -n "$NAMESPACE" --no-headers | while read -r line; do
            local hosts
            hosts=$(echo "$line" | awk '{print $3}')
            local address
            address=$(echo "$line" | awk '{print $4}')
            echo "  $hosts -> $address"
        done
    else
        echo "  使用端口转发访问服务:"
        echo "  kubectl port-forward svc/${HELM_RELEASE}-user-service 3001:3001 -n $NAMESPACE"
        echo "  kubectl port-forward svc/${HELM_RELEASE}-product-service 3002:3002 -n $NAMESPACE"
    fi
    echo ""
    
    echo "监控访问:"
    echo "  Grafana: kubectl port-forward svc/kube-prometheus-stack-grafana 3000:80 -n monitoring"
    echo "  Prometheus: kubectl port-forward svc/kube-prometheus-stack-prometheus 9090:9090 -n monitoring"
    echo ""
    
    echo "验证报告位置: $REPORT_DIR"
}

# 主函数
main() {
    log_info "开始验证云原生电商平台部署..."
    
    create_report_dir
    
    # 基础验证
    check_k8s_connection
    verify_namespace
    verify_helm_release
    
    # 组件验证
    verify_pods
    verify_services
    verify_ingress
    
    # 功能验证
    health_check
    verify_database
    verify_monitoring
    
    # 性能测试
    performance_test
    
    # 生成报告
    generate_report
    show_verification_results
    
    log_success "项目验证完成！"
}

# 执行主函数
main "$@"
