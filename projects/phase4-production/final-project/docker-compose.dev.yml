version: '3.8'

services:
  # MariaDB 数据库
  mariadb:
    image: mariadb:10.11
    container_name: ecommerce-mariadb
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ecommerce
      MYSQL_USER: ecommerce
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./scripts/database/init-mariadb.sql:/docker-entrypoint-initdb.d/init-mariadb.sql
      - ./scripts/database/my.cnf:/etc/mysql/conf.d/my.cnf
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-prootpassword"]
      timeout: 20s
      retries: 10

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ecommerce-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./scripts/database/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.11-management
    container_name: ecommerce-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password
      RABBITMQ_DEFAULT_VHOST: ecommerce
    ports:
      - "5672:5672"   # AMQP端口
      - "15672:15672" # 管理界面端口
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./scripts/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
      - ./scripts/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      timeout: 30s
      retries: 5

  # 用户服务
  user-service:
    build:
      context: ./applications/backend/user-service
      dockerfile: Dockerfile
    container_name: ecommerce-user-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_NAME=ecommerce_user
      - DB_USERNAME=ecommerce
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=admin
      - RABBITMQ_PASSWORD=password
      - JWT_SECRET=your-secret-key-here
    ports:
      - "8081:8080"
    depends_on:
      mariadb:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - ecommerce-network
    volumes:
      - ./logs/user-service:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 60s

  # 商品服务
  product-service:
    build:
      context: ./applications/backend/product-service
      dockerfile: Dockerfile
    container_name: ecommerce-product-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_NAME=ecommerce_product
      - DB_USERNAME=ecommerce
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=admin
      - RABBITMQ_PASSWORD=password
    ports:
      - "8082:8080"
    depends_on:
      mariadb:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - ecommerce-network
    volumes:
      - ./logs/product-service:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 60s

  # 订单服务
  order-service:
    build:
      context: ./applications/backend/order-service
      dockerfile: Dockerfile
    container_name: ecommerce-order-service
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_NAME=ecommerce_order
      - DB_USERNAME=ecommerce
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=admin
      - RABBITMQ_PASSWORD=password
      - USER_SERVICE_URL=http://user-service:8080
      - PRODUCT_SERVICE_URL=http://product-service:8080
    ports:
      - "8083:8080"
    depends_on:
      mariadb:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      user-service:
        condition: service_healthy
      product-service:
        condition: service_healthy
    networks:
      - ecommerce-network
    volumes:
      - ./logs/order-service:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 60s

  # API网关
  api-gateway:
    build:
      context: ./applications/shared/api-gateway
      dockerfile: Dockerfile
    container_name: ecommerce-api-gateway
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - USER_SERVICE_URL=http://user-service:8080
      - PRODUCT_SERVICE_URL=http://product-service:8080
      - ORDER_SERVICE_URL=http://order-service:8080
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "8080:8080"
    depends_on:
      user-service:
        condition: service_healthy
      product-service:
        condition: service_healthy
      order-service:
        condition: service_healthy
    networks:
      - ecommerce-network
    volumes:
      - ./logs/api-gateway:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # 前端应用 (开发模式)
  frontend-dev:
    build:
      context: ./applications/frontend/web-app
      dockerfile: Dockerfile.dev
    container_name: ecommerce-frontend-dev
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8080
    ports:
      - "3000:3000"
    volumes:
      - ./applications/frontend/web-app:/app
      - /app/node_modules
    networks:
      - ecommerce-network
    command: npm run dev

volumes:
  mariadb_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local

networks:
  ecommerce-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
