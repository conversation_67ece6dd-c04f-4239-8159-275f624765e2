package com.ecommerce.userservice.service;

import com.ecommerce.userservice.dto.UserRegistrationRequest;
import com.ecommerce.userservice.dto.UserResponse;
import com.ecommerce.userservice.entity.User;
import com.ecommerce.userservice.exception.BadRequestException;
import com.ecommerce.userservice.repository.UserRepository;
import com.ecommerce.userservice.service.impl.UserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 用户服务测试
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private UserServiceImpl userService;

    private UserRegistrationRequest validRegistrationRequest;
    private User testUser;

    @BeforeEach
    void setUp() {
        validRegistrationRequest = new UserRegistrationRequest();
        validRegistrationRequest.setUsername("testuser");
        validRegistrationRequest.setEmail("<EMAIL>");
        validRegistrationRequest.setPassword("Password123");
        validRegistrationRequest.setConfirmPassword("Password123");
        validRegistrationRequest.setPhone("13800138000");

        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPasswordHash("hashedPassword");
        testUser.setPhone("13800138000");
        testUser.setStatus(User.UserStatus.ACTIVE);
    }

    @Test
    void registerUser_Success() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(userRepository.existsByPhone(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("hashedPassword");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // When
        UserResponse result = userService.registerUser(validRegistrationRequest);

        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        verify(userRepository).save(any(User.class));
    }

    @Test
    void registerUser_PasswordMismatch_ThrowsException() {
        // Given
        validRegistrationRequest.setConfirmPassword("DifferentPassword");

        // When & Then
        BadRequestException exception = assertThrows(
                BadRequestException.class,
                () -> userService.registerUser(validRegistrationRequest)
        );
        assertEquals("密码和确认密码不匹配", exception.getMessage());
    }

    @Test
    void registerUser_UsernameExists_ThrowsException() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(true);

        // When & Then
        BadRequestException exception = assertThrows(
                BadRequestException.class,
                () -> userService.registerUser(validRegistrationRequest)
        );
        assertEquals("用户名已存在: testuser", exception.getMessage());
    }

    @Test
    void registerUser_EmailExists_ThrowsException() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        // When & Then
        BadRequestException exception = assertThrows(
                BadRequestException.class,
                () -> userService.registerUser(validRegistrationRequest)
        );
        assertEquals("邮箱已存在: <EMAIL>", exception.getMessage());
    }

    @Test
    void existsByUsername_ReturnsTrue() {
        // Given
        when(userRepository.existsByUsername("testuser")).thenReturn(true);

        // When
        boolean result = userService.existsByUsername("testuser");

        // Then
        assertTrue(result);
    }

    @Test
    void existsByUsername_ReturnsFalse() {
        // Given
        when(userRepository.existsByUsername("newuser")).thenReturn(false);

        // When
        boolean result = userService.existsByUsername("newuser");

        // Then
        assertFalse(result);
    }

    @Test
    void existsByEmail_ReturnsTrue() {
        // Given
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // When
        boolean result = userService.existsByEmail("<EMAIL>");

        // Then
        assertTrue(result);
    }

    @Test
    void getUserById_Success() {
        // Given
        when(userRepository.findUserWithFullInfo(1L)).thenReturn(Optional.of(testUser));

        // When
        UserResponse result = userService.getUserById(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("testuser", result.getUsername());
    }
}
