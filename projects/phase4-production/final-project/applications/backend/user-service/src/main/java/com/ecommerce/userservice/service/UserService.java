package com.ecommerce.userservice.service;

import com.ecommerce.userservice.dto.*;
import com.ecommerce.userservice.entity.User;
import com.ecommerce.userservice.entity.UserAddress;
import com.ecommerce.userservice.entity.UserProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户注册
     */
    UserResponse registerUser(UserRegistrationRequest request);

    /**
     * 用户登录
     */
    JwtAuthenticationResponse authenticateUser(UserLoginRequest request);

    /**
     * 刷新令牌
     */
    JwtAuthenticationResponse refreshToken(String refreshToken);

    /**
     * 根据ID获取用户信息
     */
    UserResponse getUserById(Long userId);

    /**
     * 根据用户名获取用户信息
     */
    UserResponse getUserByUsername(String username);

    /**
     * 获取当前用户信息
     */
    UserResponse getCurrentUser(Long userId);

    /**
     * 更新用户基本信息
     */
    UserResponse updateUser(Long userId, UserUpdateRequest request);

    /**
     * 更新用户密码
     */
    void updatePassword(Long userId, PasswordUpdateRequest request);

    /**
     * 更新用户详细信息
     */
    UserProfileResponse updateUserProfile(Long userId, UserProfileUpdateRequest request);

    /**
     * 获取用户地址列表
     */
    List<UserAddressResponse> getUserAddresses(Long userId);

    /**
     * 添加用户地址
     */
    UserAddressResponse addUserAddress(Long userId, UserAddressRequest request);

    /**
     * 更新用户地址
     */
    UserAddressResponse updateUserAddress(Long userId, Long addressId, UserAddressRequest request);

    /**
     * 删除用户地址
     */
    void deleteUserAddress(Long userId, Long addressId);

    /**
     * 设置默认地址
     */
    void setDefaultAddress(Long userId, Long addressId);

    /**
     * 获取默认地址
     */
    UserAddressResponse getDefaultAddress(Long userId);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 分页查询用户
     */
    Page<UserResponse> getUsers(Pageable pageable);

    /**
     * 搜索用户
     */
    Page<UserResponse> searchUsers(String keyword, Pageable pageable);

    /**
     * 更新用户状态
     */
    void updateUserStatus(Long userId, User.UserStatus status);

    /**
     * 记录用户登录
     */
    void recordUserLogin(Long userId);

    /**
     * 用户统计信息
     */
    UserStatsResponse getUserStats();

    /**
     * 验证用户身份
     */
    boolean validateUser(String usernameOrEmail, String password);
}
