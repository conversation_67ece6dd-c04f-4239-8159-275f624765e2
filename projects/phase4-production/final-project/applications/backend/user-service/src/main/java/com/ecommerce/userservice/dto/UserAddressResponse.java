package com.ecommerce.userservice.dto;

import com.ecommerce.userservice.entity.UserAddress;

/**
 * 用户地址响应DTO
 */
public class UserAddressResponse {

    private Long id;
    private String name;
    private String phone;
    private String province;
    private String city;
    private String district;
    private String detailAddress;
    private String fullAddress;
    private String postalCode;
    private Boolean isDefault;

    // 构造函数
    public UserAddressResponse() {}

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public String getFullAddress() {
        return fullAddress;
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    // 静态工厂方法
    public static UserAddressResponse fromEntity(UserAddress address) {
        if (address == null) {
            return null;
        }

        UserAddressResponse response = new UserAddressResponse();
        response.setId(address.getId());
        response.setName(address.getName());
        response.setPhone(address.getPhone());
        response.setProvince(address.getProvince());
        response.setCity(address.getCity());
        response.setDistrict(address.getDistrict());
        response.setDetailAddress(address.getDetailAddress());
        response.setFullAddress(address.getFullAddress());
        response.setPostalCode(address.getPostalCode());
        response.setIsDefault(address.getIsDefault());

        return response;
    }

    @Override
    public String toString() {
        return "UserAddressResponse{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", fullAddress='" + fullAddress + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }
}
