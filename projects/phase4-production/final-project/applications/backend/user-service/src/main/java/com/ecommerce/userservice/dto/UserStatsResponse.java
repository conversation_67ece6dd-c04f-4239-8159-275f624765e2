package com.ecommerce.userservice.dto;

/**
 * 用户统计响应DTO
 */
public class UserStatsResponse {

    private long totalUsers;
    private long activeUsers;
    private long inactiveUsers;
    private long bannedUsers;
    private long todayRegistrations;
    private long thisWeekRegistrations;
    private long thisMonthRegistrations;
    private long thisYearRegistrations;

    // 构造函数
    public UserStatsResponse() {}

    public UserStatsResponse(long totalUsers, long activeUsers, long inactiveUsers, long bannedUsers,
                            long todayRegistrations, long thisWeekRegistrations, 
                            long thisMonthRegistrations, long thisYearRegistrations) {
        this.totalUsers = totalUsers;
        this.activeUsers = activeUsers;
        this.inactiveUsers = inactiveUsers;
        this.bannedUsers = bannedUsers;
        this.todayRegistrations = todayRegistrations;
        this.thisWeekRegistrations = thisWeekRegistrations;
        this.thisMonthRegistrations = thisMonthRegistrations;
        this.thisYearRegistrations = thisYearRegistrations;
    }

    // Getters and Setters
    public long getTotalUsers() {
        return totalUsers;
    }

    public void setTotalUsers(long totalUsers) {
        this.totalUsers = totalUsers;
    }

    public long getActiveUsers() {
        return activeUsers;
    }

    public void setActiveUsers(long activeUsers) {
        this.activeUsers = activeUsers;
    }

    public long getInactiveUsers() {
        return inactiveUsers;
    }

    public void setInactiveUsers(long inactiveUsers) {
        this.inactiveUsers = inactiveUsers;
    }

    public long getBannedUsers() {
        return bannedUsers;
    }

    public void setBannedUsers(long bannedUsers) {
        this.bannedUsers = bannedUsers;
    }

    public long getTodayRegistrations() {
        return todayRegistrations;
    }

    public void setTodayRegistrations(long todayRegistrations) {
        this.todayRegistrations = todayRegistrations;
    }

    public long getThisWeekRegistrations() {
        return thisWeekRegistrations;
    }

    public void setThisWeekRegistrations(long thisWeekRegistrations) {
        this.thisWeekRegistrations = thisWeekRegistrations;
    }

    public long getThisMonthRegistrations() {
        return thisMonthRegistrations;
    }

    public void setThisMonthRegistrations(long thisMonthRegistrations) {
        this.thisMonthRegistrations = thisMonthRegistrations;
    }

    public long getThisYearRegistrations() {
        return thisYearRegistrations;
    }

    public void setThisYearRegistrations(long thisYearRegistrations) {
        this.thisYearRegistrations = thisYearRegistrations;
    }

    @Override
    public String toString() {
        return "UserStatsResponse{" +
                "totalUsers=" + totalUsers +
                ", activeUsers=" + activeUsers +
                ", inactiveUsers=" + inactiveUsers +
                ", bannedUsers=" + bannedUsers +
                ", todayRegistrations=" + todayRegistrations +
                ", thisWeekRegistrations=" + thisWeekRegistrations +
                ", thisMonthRegistrations=" + thisMonthRegistrations +
                ", thisYearRegistrations=" + thisYearRegistrations +
                '}';
    }
}
