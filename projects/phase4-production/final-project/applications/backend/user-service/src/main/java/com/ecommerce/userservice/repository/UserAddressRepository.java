package com.ecommerce.userservice.repository;

import com.ecommerce.userservice.entity.UserAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户地址数据访问接口
 */
@Repository
public interface UserAddressRepository extends JpaRepository<UserAddress, Long> {

    /**
     * 根据用户ID查找所有地址
     */
    List<UserAddress> findByUserIdOrderByIsDefaultDescCreatedAtDesc(Long userId);

    /**
     * 根据用户ID查找默认地址
     */
    Optional<UserAddress> findByUserIdAndIsDefaultTrue(Long userId);

    /**
     * 根据用户ID和地址ID查找地址
     */
    Optional<UserAddress> findByIdAndUserId(Long addressId, Long userId);

    /**
     * 统计用户的地址数量
     */
    long countByUserId(Long userId);

    /**
     * 检查用户是否有默认地址
     */
    boolean existsByUserIdAndIsDefaultTrue(Long userId);

    /**
     * 将用户的所有地址设置为非默认
     */
    @Modifying
    @Query("UPDATE UserAddress ua SET ua.isDefault = false WHERE ua.user.id = :userId")
    int unsetAllDefaultAddresses(@Param("userId") Long userId);

    /**
     * 设置指定地址为默认地址
     */
    @Modifying
    @Query("UPDATE UserAddress ua SET ua.isDefault = true WHERE ua.id = :addressId AND ua.user.id = :userId")
    int setDefaultAddress(@Param("addressId") Long addressId, @Param("userId") Long userId);

    /**
     * 根据省份查找地址
     */
    List<UserAddress> findByProvince(String province);

    /**
     * 根据城市查找地址
     */
    List<UserAddress> findByProvinceAndCity(String province, String city);

    /**
     * 根据收货人姓名模糊查询
     */
    @Query("SELECT ua FROM UserAddress ua WHERE ua.user.id = :userId AND ua.name LIKE %:name%")
    List<UserAddress> findByUserIdAndNameContaining(@Param("userId") Long userId, @Param("name") String name);

    /**
     * 根据收货人电话查找地址
     */
    List<UserAddress> findByUserIdAndPhone(Long userId, String phone);

    /**
     * 删除用户的所有地址
     */
    @Modifying
    @Query("DELETE FROM UserAddress ua WHERE ua.user.id = :userId")
    int deleteAllByUserId(@Param("userId") Long userId);

    /**
     * 查找用户的非默认地址
     */
    List<UserAddress> findByUserIdAndIsDefaultFalse(Long userId);

    /**
     * 统计各省份的地址数量
     */
    @Query("SELECT ua.province, COUNT(ua) FROM UserAddress ua GROUP BY ua.province")
    List<Object[]> countAddressesByProvince();

    /**
     * 查找最近添加的地址
     */
    @Query("SELECT ua FROM UserAddress ua WHERE ua.user.id = :userId ORDER BY ua.createdAt DESC")
    List<UserAddress> findRecentAddresses(@Param("userId") Long userId);
}
