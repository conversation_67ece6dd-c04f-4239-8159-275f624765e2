package com.ecommerce.userservice.dto;

import jakarta.validation.constraints.Size;

/**
 * 用户详细信息更新请求DTO
 */
public class UserProfileUpdateRequest {

    @Size(max = 50, message = "名字长度不能超过50个字符")
    private String firstName;

    @Size(max = 50, message = "姓氏长度不能超过50个字符")
    private String lastName;

    private String avatarUrl;
    
    private String birthDate; // yyyy-MM-dd格式
    
    private String gender; // MALE, FEMALE, OTHER

    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;

    // 构造函数
    public UserProfileUpdateRequest() {}

    public UserProfileUpdateRequest(String firstName, String lastName, String avatarUrl, 
                                   String birthDate, String gender, String bio) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.avatarUrl = avatarUrl;
        this.birthDate = birthDate;
        this.gender = gender;
        this.bio = bio;
    }

    // Getters and Setters
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    @Override
    public String toString() {
        return "UserProfileUpdateRequest{" +
                "firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", birthDate='" + birthDate + '\'' +
                ", gender='" + gender + '\'' +
                ", bio='" + bio + '\'' +
                '}';
    }
}
