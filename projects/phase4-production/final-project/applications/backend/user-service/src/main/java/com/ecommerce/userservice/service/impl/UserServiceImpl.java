package com.ecommerce.userservice.service.impl;

import com.ecommerce.userservice.dto.*;
import com.ecommerce.userservice.entity.User;
import com.ecommerce.userservice.entity.UserAddress;
import com.ecommerce.userservice.entity.UserProfile;
import com.ecommerce.userservice.exception.ResourceNotFoundException;
import com.ecommerce.userservice.exception.BadRequestException;
import com.ecommerce.userservice.repository.UserRepository;
import com.ecommerce.userservice.repository.UserAddressRepository;
import com.ecommerce.userservice.security.JwtTokenProvider;
import com.ecommerce.userservice.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserAddressRepository userAddressRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public UserResponse registerUser(UserRegistrationRequest request) {
        logger.info("注册新用户: {}", request.getUsername());

        // 验证密码匹配
        if (!request.isPasswordMatched()) {
            throw new BadRequestException("密码和确认密码不匹配");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BadRequestException("用户名已存在: " + request.getUsername());
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BadRequestException("邮箱已存在: " + request.getEmail());
        }

        // 检查手机号是否已存在（如果提供）
        if (request.getPhone() != null && userRepository.existsByPhone(request.getPhone())) {
            throw new BadRequestException("手机号已存在: " + request.getPhone());
        }

        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setPhone(request.getPhone());
        user.setStatus(User.UserStatus.ACTIVE);

        // 创建用户详细信息
        UserProfile profile = new UserProfile(user);
        user.setProfile(profile);

        // 保存用户
        User savedUser = userRepository.save(user);

        logger.info("用户注册成功: {}", savedUser.getUsername());
        return UserResponse.fromEntity(savedUser);
    }

    @Override
    public JwtAuthenticationResponse authenticateUser(UserLoginRequest request) {
        logger.info("用户登录: {}", request.getUsernameOrEmail());

        // 认证用户
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                request.getUsernameOrEmail(),
                request.getPassword()
            )
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 获取用户信息
        User user = userRepository.findByUsernameOrEmail(request.getUsernameOrEmail(), request.getUsernameOrEmail())
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // 生成JWT令牌
        String accessToken = tokenProvider.generateTokenFromUserId(user.getId(), user.getUsername());
        String refreshToken = tokenProvider.generateRefreshToken(user.getId(), user.getUsername());

        // 计算过期时间
        long expiresIn = tokenProvider.getTokenRemainingTime(accessToken);

        // 记录登录时间
        recordUserLogin(user.getId());

        // 缓存用户信息
        cacheUserInfo(user);

        UserResponse userResponse = UserResponse.fromEntity(user);
        
        logger.info("用户登录成功: {}", user.getUsername());
        return new JwtAuthenticationResponse(accessToken, refreshToken, expiresIn, userResponse);
    }

    @Override
    public JwtAuthenticationResponse refreshToken(String refreshToken) {
        logger.info("刷新令牌");

        if (!tokenProvider.validateToken(refreshToken) || !tokenProvider.isRefreshToken(refreshToken)) {
            throw new BadRequestException("无效的刷新令牌");
        }

        Long userId = tokenProvider.getUserIdFromToken(refreshToken);
        String username = tokenProvider.getUsernameFromToken(refreshToken);

        // 生成新的访问令牌
        String newAccessToken = tokenProvider.generateTokenFromUserId(userId, username);
        long expiresIn = tokenProvider.getTokenRemainingTime(newAccessToken);

        // 获取用户信息
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        UserResponse userResponse = UserResponse.fromEntity(user);

        return new JwtAuthenticationResponse(newAccessToken, refreshToken, expiresIn, userResponse);
    }

    @Override
    @Cacheable(value = "users", key = "#userId")
    public UserResponse getUserById(Long userId) {
        logger.debug("获取用户信息: {}", userId);

        User user = userRepository.findUserWithFullInfo(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));

        UserResponse response = UserResponse.fromEntity(user);
        
        // 设置详细信息
        if (user.getProfile() != null) {
            response.setProfile(UserProfileResponse.fromEntity(user.getProfile()));
        }

        // 设置地址信息
        if (user.getAddresses() != null && !user.getAddresses().isEmpty()) {
            List<UserAddressResponse> addresses = user.getAddresses().stream()
                    .map(UserAddressResponse::fromEntity)
                    .collect(Collectors.toList());
            response.setAddresses(addresses);
        }

        return response;
    }

    @Override
    public UserResponse getUserByUsername(String username) {
        logger.debug("根据用户名获取用户信息: {}", username);

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在: " + username));

        return UserResponse.fromEntity(user);
    }

    @Override
    public UserResponse getCurrentUser(Long userId) {
        return getUserById(userId);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse updateUser(Long userId, UserUpdateRequest request) {
        logger.info("更新用户信息: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));

        // 更新邮箱
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new BadRequestException("邮箱已存在: " + request.getEmail());
            }
            user.setEmail(request.getEmail());
        }

        // 更新手机号
        if (request.getPhone() != null && !request.getPhone().equals(user.getPhone())) {
            if (userRepository.existsByPhone(request.getPhone())) {
                throw new BadRequestException("手机号已存在: " + request.getPhone());
            }
            user.setPhone(request.getPhone());
        }

        User updatedUser = userRepository.save(user);
        logger.info("用户信息更新成功: {}", updatedUser.getUsername());

        return UserResponse.fromEntity(updatedUser);
    }

    @Override
    public void updatePassword(Long userId, PasswordUpdateRequest request) {
        logger.info("更新用户密码: {}", userId);

        if (!request.isPasswordMatched()) {
            throw new BadRequestException("新密码和确认密码不匹配");
        }

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));

        // 验证当前密码
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPasswordHash())) {
            throw new BadRequestException("当前密码不正确");
        }

        // 更新密码
        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);

        // 清除缓存
        clearUserCache(userId);

        logger.info("用户密码更新成功: {}", user.getUsername());
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public UserProfileResponse updateUserProfile(Long userId, UserProfileUpdateRequest request) {
        logger.info("更新用户详细信息: {}", userId);

        User user = userRepository.findUserWithProfile(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));

        UserProfile profile = user.getProfile();
        if (profile == null) {
            profile = new UserProfile(user);
            user.setProfile(profile);
        }

        // 更新详细信息
        if (request.getFirstName() != null) {
            profile.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            profile.setLastName(request.getLastName());
        }
        if (request.getAvatarUrl() != null) {
            profile.setAvatarUrl(request.getAvatarUrl());
        }
        if (request.getBirthDate() != null) {
            try {
                LocalDate birthDate = LocalDate.parse(request.getBirthDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                profile.setBirthDate(birthDate);
            } catch (Exception e) {
                throw new BadRequestException("生日格式不正确，请使用yyyy-MM-dd格式");
            }
        }
        if (request.getGender() != null) {
            try {
                UserProfile.Gender gender = UserProfile.Gender.valueOf(request.getGender().toUpperCase());
                profile.setGender(gender);
            } catch (IllegalArgumentException e) {
                throw new BadRequestException("性别值不正确，请使用MALE、FEMALE或OTHER");
            }
        }
        if (request.getBio() != null) {
            profile.setBio(request.getBio());
        }

        userRepository.save(user);
        logger.info("用户详细信息更新成功: {}", user.getUsername());

        return UserProfileResponse.fromEntity(profile);
    }

    @Override
    public List<UserAddressResponse> getUserAddresses(Long userId) {
        logger.debug("获取用户地址列表: {}", userId);

        // 验证用户存在
        if (!userRepository.existsById(userId)) {
            throw new ResourceNotFoundException("用户不存在，ID: " + userId);
        }

        List<UserAddress> addresses = userAddressRepository.findByUserIdOrderByIsDefaultDescCreatedAtDesc(userId);
        return addresses.stream()
                .map(UserAddressResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public UserAddressResponse addUserAddress(Long userId, UserAddressRequest request) {
        logger.info("添加用户地址: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));

        // 如果设置为默认地址，先取消其他默认地址
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            userAddressRepository.unsetAllDefaultAddresses(userId);
        }

        // 创建新地址
        UserAddress address = new UserAddress();
        address.setUser(user);
        address.setName(request.getName());
        address.setPhone(request.getPhone());
        address.setProvince(request.getProvince());
        address.setCity(request.getCity());
        address.setDistrict(request.getDistrict());
        address.setDetailAddress(request.getDetailAddress());
        address.setPostalCode(request.getPostalCode());
        address.setIsDefault(request.getIsDefault());

        UserAddress savedAddress = userAddressRepository.save(address);
        logger.info("用户地址添加成功: {}", savedAddress.getId());

        return UserAddressResponse.fromEntity(savedAddress);
    }

    @Override
    public UserAddressResponse updateUserAddress(Long userId, Long addressId, UserAddressRequest request) {
        logger.info("更新用户地址: userId={}, addressId={}", userId, addressId);

        UserAddress address = userAddressRepository.findByIdAndUserId(addressId, userId)
                .orElseThrow(() -> new ResourceNotFoundException("地址不存在，ID: " + addressId));

        // 如果设置为默认地址，先取消其他默认地址
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            userAddressRepository.unsetAllDefaultAddresses(userId);
        }

        // 更新地址信息
        address.setName(request.getName());
        address.setPhone(request.getPhone());
        address.setProvince(request.getProvince());
        address.setCity(request.getCity());
        address.setDistrict(request.getDistrict());
        address.setDetailAddress(request.getDetailAddress());
        address.setPostalCode(request.getPostalCode());
        address.setIsDefault(request.getIsDefault());

        UserAddress updatedAddress = userAddressRepository.save(address);
        logger.info("用户地址更新成功: {}", updatedAddress.getId());

        return UserAddressResponse.fromEntity(updatedAddress);
    }

    @Override
    public void deleteUserAddress(Long userId, Long addressId) {
        logger.info("删除用户地址: userId={}, addressId={}", userId, addressId);

        UserAddress address = userAddressRepository.findByIdAndUserId(addressId, userId)
                .orElseThrow(() -> new ResourceNotFoundException("地址不存在，ID: " + addressId));

        userAddressRepository.delete(address);
        logger.info("用户地址删除成功: {}", addressId);
    }

    @Override
    public void setDefaultAddress(Long userId, Long addressId) {
        logger.info("设置默认地址: userId={}, addressId={}", userId, addressId);

        // 验证地址存在且属于该用户
        if (!userAddressRepository.findByIdAndUserId(addressId, userId).isPresent()) {
            throw new ResourceNotFoundException("地址不存在，ID: " + addressId);
        }

        // 取消所有默认地址
        userAddressRepository.unsetAllDefaultAddresses(userId);

        // 设置新的默认地址
        userAddressRepository.setDefaultAddress(addressId, userId);
        logger.info("默认地址设置成功: {}", addressId);
    }

    @Override
    public UserAddressResponse getDefaultAddress(Long userId) {
        logger.debug("获取默认地址: {}", userId);

        UserAddress defaultAddress = userAddressRepository.findByUserIdAndIsDefaultTrue(userId)
                .orElse(null);

        return defaultAddress != null ? UserAddressResponse.fromEntity(defaultAddress) : null;
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhone(phone);
    }

    @Override
    public Page<UserResponse> getUsers(Pageable pageable) {
        logger.debug("分页获取用户列表");

        Page<User> users = userRepository.findAll(pageable);
        return users.map(UserResponse::fromEntity);
    }

    @Override
    public Page<UserResponse> searchUsers(String keyword, Pageable pageable) {
        logger.debug("搜索用户: {}", keyword);

        Page<User> users = userRepository.searchUsers(keyword, pageable);
        return users.map(UserResponse::fromEntity);
    }

    @Override
    @CacheEvict(value = "users", key = "#userId")
    public void updateUserStatus(Long userId, User.UserStatus status) {
        logger.info("更新用户状态: userId={}, status={}", userId, status);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));

        user.setStatus(status);
        userRepository.save(user);

        logger.info("用户状态更新成功: {}", user.getUsername());
    }

    @Override
    public void recordUserLogin(Long userId) {
        logger.debug("记录用户登录: {}", userId);

        userRepository.updateLastLoginTime(userId, LocalDateTime.now());
    }

    @Override
    public UserStatsResponse getUserStats() {
        logger.debug("获取用户统计信息");

        long totalUsers = userRepository.countTotalUsers();
        long activeUsers = userRepository.countByStatus(User.UserStatus.ACTIVE);
        long inactiveUsers = userRepository.countByStatus(User.UserStatus.INACTIVE);
        long bannedUsers = userRepository.countByStatus(User.UserStatus.BANNED);
        long todayRegistrations = userRepository.countTodayRegistrations();

        // 计算本周、本月、本年注册数（简化实现）
        LocalDateTime weekStart = LocalDateTime.now().minusDays(7);
        LocalDateTime monthStart = LocalDateTime.now().minusDays(30);
        LocalDateTime yearStart = LocalDateTime.now().minusDays(365);

        long thisWeekRegistrations = userRepository.findByCreatedAtBetween(weekStart, LocalDateTime.now()).size();
        long thisMonthRegistrations = userRepository.findByCreatedAtBetween(monthStart, LocalDateTime.now()).size();
        long thisYearRegistrations = userRepository.findByCreatedAtBetween(yearStart, LocalDateTime.now()).size();

        return new UserStatsResponse(totalUsers, activeUsers, inactiveUsers, bannedUsers,
                todayRegistrations, thisWeekRegistrations, thisMonthRegistrations, thisYearRegistrations);
    }

    @Override
    public boolean validateUser(String usernameOrEmail, String password) {
        logger.debug("验证用户身份: {}", usernameOrEmail);

        User user = userRepository.findByUsernameOrEmail(usernameOrEmail, usernameOrEmail)
                .orElse(null);

        if (user == null || !user.isActive()) {
            return false;
        }

        return passwordEncoder.matches(password, user.getPasswordHash());
    }

    // 私有辅助方法
    private void cacheUserInfo(User user) {
        String cacheKey = "user:info:" + user.getId();
        redisTemplate.opsForValue().set(cacheKey, UserResponse.fromEntity(user), 30, TimeUnit.MINUTES);
    }

    private void clearUserCache(Long userId) {
        String cacheKey = "user:info:" + userId;
        redisTemplate.delete(cacheKey);

        // 清除Spring Cache
        redisTemplate.delete("users::" + userId);
    }
}
