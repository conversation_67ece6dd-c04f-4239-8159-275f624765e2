package com.ecommerce.userservice.controller;

import com.ecommerce.userservice.dto.*;
import com.ecommerce.userservice.entity.User;
import com.ecommerce.userservice.exception.BadRequestException;
import com.ecommerce.userservice.security.UserPrincipal;
import com.ecommerce.userservice.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "user-service");
        response.put("timestamp", String.valueOf(System.currentTimeMillis()));
        return ResponseEntity.ok(response);
    }

    /**
     * 用户注册
     */
    @PostMapping("/auth/register")
    public ResponseEntity<ApiResponse<UserResponse>> registerUser(@Valid @RequestBody UserRegistrationRequest request) {
        logger.info("用户注册请求: {}", request.getUsername());

        UserResponse userResponse = userService.registerUser(request);

        ApiResponse<UserResponse> response = new ApiResponse<>(
                true, "用户注册成功", userResponse
        );

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    /**
     * 用户登录
     */
    @PostMapping("/auth/login")
    public ResponseEntity<ApiResponse<JwtAuthenticationResponse>> authenticateUser(
            @Valid @RequestBody UserLoginRequest request) {
        logger.info("用户登录请求: {}", request.getUsernameOrEmail());

        JwtAuthenticationResponse jwtResponse = userService.authenticateUser(request);

        ApiResponse<JwtAuthenticationResponse> response = new ApiResponse<>(
                true, "登录成功", jwtResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/auth/refresh")
    public ResponseEntity<ApiResponse<JwtAuthenticationResponse>> refreshToken(
            @RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");
        logger.info("刷新令牌请求");

        JwtAuthenticationResponse jwtResponse = userService.refreshToken(refreshToken);

        ApiResponse<JwtAuthenticationResponse> response = new ApiResponse<>(
                true, "令牌刷新成功", jwtResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserResponse>> getCurrentUser(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取当前用户信息: {}", currentUser.getId());

        UserResponse userResponse = userService.getCurrentUser(currentUser.getId());

        ApiResponse<UserResponse> response = new ApiResponse<>(
                true, "获取用户信息成功", userResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/me")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserResponse>> updateCurrentUser(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @Valid @RequestBody UserUpdateRequest request) {
        logger.info("更新用户信息: {}", currentUser.getId());

        UserResponse userResponse = userService.updateUser(currentUser.getId(), request);

        ApiResponse<UserResponse> response = new ApiResponse<>(
                true, "用户信息更新成功", userResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 更新密码
     */
    @PutMapping("/me/password")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> updatePassword(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @Valid @RequestBody PasswordUpdateRequest request) {
        logger.info("更新用户密码: {}", currentUser.getId());

        userService.updatePassword(currentUser.getId(), request);

        ApiResponse<Void> response = new ApiResponse<>(
                true, "密码更新成功", null
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 更新用户详细信息
     */
    @PutMapping("/me/profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserProfileResponse>> updateUserProfile(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @Valid @RequestBody UserProfileUpdateRequest request) {
        logger.info("更新用户详细信息: {}", currentUser.getId());

        UserProfileResponse profileResponse = userService.updateUserProfile(currentUser.getId(), request);

        ApiResponse<UserProfileResponse> response = new ApiResponse<>(
                true, "用户详细信息更新成功", profileResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 获取用户地址列表
     */
    @GetMapping("/me/addresses")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<UserAddressResponse>>> getUserAddresses(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户地址列表: {}", currentUser.getId());

        List<UserAddressResponse> addresses = userService.getUserAddresses(currentUser.getId());

        ApiResponse<List<UserAddressResponse>> response = new ApiResponse<>(
                true, "获取地址列表成功", addresses
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 添加用户地址
     */
    @PostMapping("/me/addresses")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserAddressResponse>> addUserAddress(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @Valid @RequestBody UserAddressRequest request) {
        logger.info("添加用户地址: {}", currentUser.getId());

        UserAddressResponse addressResponse = userService.addUserAddress(currentUser.getId(), request);

        ApiResponse<UserAddressResponse> response = new ApiResponse<>(
                true, "地址添加成功", addressResponse
        );

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    /**
     * 更新用户地址
     */
    @PutMapping("/me/addresses/{addressId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserAddressResponse>> updateUserAddress(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @PathVariable Long addressId,
            @Valid @RequestBody UserAddressRequest request) {
        logger.info("更新用户地址: userId={}, addressId={}", currentUser.getId(), addressId);

        UserAddressResponse addressResponse = userService.updateUserAddress(
                currentUser.getId(), addressId, request);

        ApiResponse<UserAddressResponse> response = new ApiResponse<>(
                true, "地址更新成功", addressResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 删除用户地址
     */
    @DeleteMapping("/me/addresses/{addressId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> deleteUserAddress(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @PathVariable Long addressId) {
        logger.info("删除用户地址: userId={}, addressId={}", currentUser.getId(), addressId);

        userService.deleteUserAddress(currentUser.getId(), addressId);

        ApiResponse<Void> response = new ApiResponse<>(
                true, "地址删除成功", null
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 设置默认地址
     */
    @PutMapping("/me/addresses/{addressId}/default")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> setDefaultAddress(
            @AuthenticationPrincipal UserPrincipal currentUser,
            @PathVariable Long addressId) {
        logger.info("设置默认地址: userId={}, addressId={}", currentUser.getId(), addressId);

        userService.setDefaultAddress(currentUser.getId(), addressId);

        ApiResponse<Void> response = new ApiResponse<>(
                true, "默认地址设置成功", null
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 获取默认地址
     */
    @GetMapping("/me/addresses/default")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserAddressResponse>> getDefaultAddress(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取默认地址: {}", currentUser.getId());

        UserAddressResponse defaultAddress = userService.getDefaultAddress(currentUser.getId());

        ApiResponse<UserAddressResponse> response = new ApiResponse<>(
                true, "获取默认地址成功", defaultAddress
        );

        return ResponseEntity.ok(response);
    }

    // ==================== 管理员接口 ====================

    /**
     * 分页获取用户列表（管理员）
     */
    @GetMapping("/admin/users")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        logger.debug("管理员获取用户列表: page={}, size={}", page, size);

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<UserResponse> users = userService.getUsers(pageable);

        ApiResponse<Page<UserResponse>> response = new ApiResponse<>(
                true, "获取用户列表成功", users
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 搜索用户（管理员）
     */
    @GetMapping("/admin/users/search")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> searchUsers(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        logger.debug("管理员搜索用户: keyword={}", keyword);

        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<UserResponse> users = userService.searchUsers(keyword, pageable);

        ApiResponse<Page<UserResponse>> response = new ApiResponse<>(
                true, "搜索用户成功", users
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 根据ID获取用户信息（管理员）
     */
    @GetMapping("/admin/users/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long userId) {
        logger.debug("管理员获取用户信息: {}", userId);

        UserResponse userResponse = userService.getUserById(userId);

        ApiResponse<UserResponse> response = new ApiResponse<>(
                true, "获取用户信息成功", userResponse
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 更新用户状态（管理员）
     */
    @PutMapping("/admin/users/{userId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateUserStatus(
            @PathVariable Long userId,
            @RequestBody Map<String, String> request) {
        logger.info("管理员更新用户状态: userId={}", userId);

        String statusStr = request.get("status");
        User.UserStatus status;
        try {
            status = User.UserStatus.valueOf(statusStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new BadRequestException("无效的用户状态: " + statusStr);
        }

        userService.updateUserStatus(userId, status);

        ApiResponse<Void> response = new ApiResponse<>(
                true, "用户状态更新成功", null
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 获取用户统计信息（管理员）
     */
    @GetMapping("/admin/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserStatsResponse>> getUserStats() {
        logger.debug("管理员获取用户统计信息");

        UserStatsResponse stats = userService.getUserStats();

        ApiResponse<UserStatsResponse> response = new ApiResponse<>(
                true, "获取统计信息成功", stats
        );

        return ResponseEntity.ok(response);
    }

    // ==================== 公开接口 ====================

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/public/check-username")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> checkUsernameAvailability(
            @RequestParam String username) {
        logger.debug("检查用户名可用性: {}", username);

        boolean isAvailable = !userService.existsByUsername(username);

        Map<String, Boolean> result = new HashMap<>();
        result.put("available", isAvailable);

        ApiResponse<Map<String, Boolean>> response = new ApiResponse<>(
                true, "检查完成", result
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/public/check-email")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> checkEmailAvailability(
            @RequestParam String email) {
        logger.debug("检查邮箱可用性: {}", email);

        boolean isAvailable = !userService.existsByEmail(email);

        Map<String, Boolean> result = new HashMap<>();
        result.put("available", isAvailable);

        ApiResponse<Map<String, Boolean>> response = new ApiResponse<>(
                true, "检查完成", result
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 检查手机号是否可用
     */
    @GetMapping("/public/check-phone")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> checkPhoneAvailability(
            @RequestParam String phone) {
        logger.debug("检查手机号可用性: {}", phone);

        boolean isAvailable = !userService.existsByPhone(phone);

        Map<String, Boolean> result = new HashMap<>();
        result.put("available", isAvailable);

        ApiResponse<Map<String, Boolean>> response = new ApiResponse<>(
                true, "检查完成", result
        );

        return ResponseEntity.ok(response);
    }
}
