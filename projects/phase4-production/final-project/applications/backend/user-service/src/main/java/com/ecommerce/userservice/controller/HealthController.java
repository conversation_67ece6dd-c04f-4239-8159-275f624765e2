package com.ecommerce.userservice.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供服务健康状态检查接口
 */
@RestController
public class HealthController {

    @Autowired
    private DataSource dataSource;

    /**
     * 健康检查接口
     * 检查服务和数据库连接状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查数据库连接
            boolean dbHealthy = checkDatabaseHealth();
            
            response.put("status", dbHealthy ? "UP" : "DOWN");
            response.put("timestamp", LocalDateTime.now());
            response.put("service", "user-service");
            response.put("version", "1.0.0");
            
            Map<String, Object> checks = new HashMap<>();
            checks.put("database", dbHealthy ? "UP" : "DOWN");
            response.put("checks", checks);
            
            if (dbHealthy) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(503).body(response);
            }
            
        } catch (Exception e) {
            response.put("status", "DOWN");
            response.put("timestamp", LocalDateTime.now());
            response.put("service", "user-service");
            response.put("version", "1.0.0");
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(503).body(response);
        }
    }

    /**
     * 检查数据库健康状态
     */
    private boolean checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5); // 5秒超时
        } catch (Exception e) {
            return false;
        }
    }
}
